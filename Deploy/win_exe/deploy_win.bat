@echo off
setlocal enabledelayedexpansion

set "APP_SCRIPT=..\..\Server.py"
set "OUTPUT_NAME=AgentService"

echo checking hooks...
if not exist %~dp0..\..\.venv\Lib\site-packages\PyInstaller\hooks\hook-unstructured.py (
    copy "%~dp0hook-unstructured.py" "%~dp0..\..\.venv\Lib\site-packages\PyInstaller\hooks\"
    echo copy hook-unstructured.py into PyIntaller hooks.
)

cd %dp0..\..\
echo packaging files by pyinstaller...

%~dp0..\..\.venv\Scripts\pyinstaller.exe --noconfirm --onefile -F --name "%OUTPUT_NAME%" "%APP_SCRIPT%"

if errorlevel 1 (
    echo.
    echo PyInstaller ERROR!
    pause
    exit /b 1
)

echo.
echo packaging files by pyinstaller done.
echo out file name: %~dp0dist\%OUTPUT_NAME%.exe
echo.
echo copy config and resource files...
xcopy /E /I /Y "%~dp0..\..\Configs" "%~dp0dist\Configs"
xcopy /E /I /Y "%~dp0..\..\Resource" "%~dp0dist\Resource"
xcopy /E /I /Y "%~dp0..\..\data" "%~dp0dist\data"

if exist "%~dp0dist\Configs\env.json" (
    del "%~dp0dist\Configs\env.json"
    del "%~dp0dist\Configs\Config.py"
    echo env.json deleted.
)

echo deleting build dir...
rd /s /q "%~dp0build"
echo build dir deleted.

echo package successfully.

REM OPEN DIST DIR
explorer.exe "%~dp0dist"

pause