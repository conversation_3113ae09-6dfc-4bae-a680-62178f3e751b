version: '3'

services:
  agent-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: agent-server
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    restart: "no"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - HOST_IP=${HOST_IP}
    command: >
      sh -c "
        echo 'Current directory:' &&
        pwd &&
        echo 'Directory contents:' &&
        ls -la &&
        echo 'Python path:' &&
        python3 -c 'import sys; print(sys.path)' &&
        python3 Server.py
      "
    ports:
      - "8000:8000"
    volumes:
      - /mnt/qydata/qydisk/agent_logs:/app/logs:rw
      - /mnt/qydata/qydisk/agent_logs/archive:/app/logs/archive:rw
      - /mnt/qydata/qydisk/qyd_agents:/qyd_agents:rw
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
        compress: "true"
    networks:
      - my_shared_network

networks:
    my_shared_network:
      external: true