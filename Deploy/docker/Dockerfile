FROM python:3.11.12-slim

# 设置时区为上海
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 设置 apt 源为华为云镜像并安装系统依赖
RUN echo "deb https://repo.huaweicloud.com/debian/ bullseye main contrib non-free" > /etc/apt/sources.list \
    && echo "deb https://repo.huaweicloud.com/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list \
    && echo "deb https://repo.huaweicloud.com/debian-security bullseye-security main contrib non-free" >> /etc/apt/sources.list \
    && apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libmagic1 \
    poppler-utils \
    tesseract-ocr \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 设置pip配置
ENV PIP_DEFAULT_TIMEOUT=1000
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置pip源为华为云并升级pip
RUN pip config set global.index-url https://repo.huaweicloud.com/repository/pypi/simple \
    && pip config set global.trusted-host repo.huaweicloud.com \
    && pip install --upgrade pip

# COPY /mnt/qydata/qydisk/qyd_agents/nltk_data /root/nltk_data
# 复制依赖文件
COPY requirements*.txt ./

# 安装其他依赖
RUN pip install --no-cache-dir -r requirements.txt

# 验证关键包的安装
RUN pip list | grep -E "PyPDF2|paddlepaddle|langchain|transformers"

ENV TIKTOKEN_CACHE_DIR=/app/data/tiktoken_cache

# 复制应用文件
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

# 启动命令
CMD ["python", "Server.py"]

