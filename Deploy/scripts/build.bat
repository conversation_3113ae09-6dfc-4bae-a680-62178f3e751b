@echo off
setlocal enabledelayedexpansion

:: 获取当前脚本所在目录的绝对路径
set "SCRIPT_DIR=%~dp0"
:: 获取项目根目录（上两级目录）
for %%I in ("%SCRIPT_DIR%\..\..\") do set "PROJECT_ROOT=%%~fI"

echo 脚本目录: %SCRIPT_DIR%
echo 项目根目录: %PROJECT_ROOT%

:: 设置部署目录
set "DEPLOY_DIR=%SCRIPT_DIR%deploy_package"

:: 使用时间作为版本号
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set VERSION=%datetime:~0,8%_%datetime:~8,6%

echo 正在创建部署目录: %DEPLOY_DIR%
if exist "%DEPLOY_DIR%" rd /s /q "%DEPLOY_DIR%"
mkdir "%DEPLOY_DIR%"

cd /d "%PROJECT_ROOT%"
echo 当前工作目录: %CD%

echo 开始复制文件...

:: 复制文件，使用绝对路径
if exist "%PROJECT_ROOT%Server.py" (
    echo 复制 Server.py...
    copy "%PROJECT_ROOT%Server.py" "%DEPLOY_DIR%\" /Y
) else (
    echo Server.py 不存在: %PROJECT_ROOT%Server.py
)

if exist "%PROJECT_ROOT%requirements.txt" (
    echo 复制 requirements.txt...
    copy "%PROJECT_ROOT%requirements.txt" "%DEPLOY_DIR%\" /Y
) else (
    echo requirements.txt 不存在: %PROJECT_ROOT%requirements.txt
)

if exist "%PROJECT_ROOT%Deploy\docker\Dockerfile" (
    echo 复制 Dockerfile...
    copy "%PROJECT_ROOT%Deploy\docker\Dockerfile" "%DEPLOY_DIR%\" /Y
) else (
    echo Dockerfile 不存在: %PROJECT_ROOT%Deploy\docker\Dockerfile
)

if exist "%PROJECT_ROOT%Deploy\docker\docker-compose.yml" (
    echo 复制 docker-compose.yml...
    copy "%PROJECT_ROOT%Deploy\docker\docker-compose.yml" "%DEPLOY_DIR%\" /Y
) else (
    echo docker-compose.yml 不存在: %PROJECT_ROOT%Deploy\docker\docker-compose.yml
)

if exist "%PROJECT_ROOT%Deploy\docker\docker-compose-win.yml" (
    echo 复制 docker-compose.yml...
    copy "%PROJECT_ROOT%Deploy\docker\docker-compose-win.yml" "%DEPLOY_DIR%\" /Y
) else (
    echo docker-compose-win.yml 不存在: %PROJECT_ROOT%Deploy\docker\docker-compose-win.yml
)

:: 复制目录
for %%D in (Agents Controller Utils Configs LLM Models Validators Services Resource) do (
    if exist "%PROJECT_ROOT%%%D" (
        echo 复制 %%D 目录...
        xcopy "%PROJECT_ROOT%%%D" "%DEPLOY_DIR%\%%D\" /E /I /Y
    ) else (
        echo %%D 目录不存在: %PROJECT_ROOT%%%D
    )
)

if exist "%DEPLOY_DIR%\Configs\env.json" (
    echo 删除 env.json...
    del /q "%DEPLOY_DIR%\Configs\env.json"
)

if exist "%PROJECT_ROOT%.env" (
    echo 复制 .env...
    copy "%PROJECT_ROOT%.env" "%DEPLOY_DIR%\" /Y
) else (
    echo .env 不存在: %PROJECT_ROOT%.env
)

if exist "%PROJECT_ROOT%Deploy\scripts\deploy.sh" (
    echo 复制 deploy.sh...
    copy "%PROJECT_ROOT%Deploy\scripts\deploy.sh" "%DEPLOY_DIR%\" /Y
) else (
    echo deploy.sh 不存在: %PROJECT_ROOT%Deploy\scripts\deploy.sh
)

echo.
echo 复制完成，正在检查部署目录内容:
dir "%DEPLOY_DIR%"

echo.
echo 请检查以上输出信息确认文件复制是否成功。
pause 