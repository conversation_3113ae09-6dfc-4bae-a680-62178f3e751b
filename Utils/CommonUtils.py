import json
import re
from typing import Type, TypeVar

from langchain_core.documents import Document
from pydantic import BaseModel


def get_root_file_path(file_name: str=None) -> str:
    import os

    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    if file_name:
        return os.path.join(project_root, file_name)
    else:
        return project_root

def list_to_str(str_list: list[str]) -> str:
    """将字符串列表转换为逗号分隔的字符串"""
    return ",".join(str_list) if str_list else ""


def str_to_list(list_str: str) -> list[str]:
    """将逗号分隔的字符串转换回列表"""
    return list_str.split(",") if list_str else []

def fix_json_quotes(text: str) -> str:
    try:
        fixed_str = text.replace("“", '"').replace("”", '"')
        return fixed_str
    except Exception:
        # 如果转换失败，返回原始字符串
        return text

def remove_think_tags(text: str) -> str:
    """删除<think>标签及内容，保留其他部分"""
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL).strip()

def transform_to_document(*, id: str, content: str, metadata: dict, file_trunk_id: str = "",
                          distance: float = 0) -> Document:
    metadata["file_trunk_id"] = file_trunk_id
    metadata["distance"] = distance
    return Document(page_content=content, metadata=metadata, id=id)



def to_json_str(obj: BaseModel) -> str:
    """对象转JSON字符串
    Args:
        obj: 需要序列化的对象
    Returns:
        JSON格式字符串
    """
    return obj.model_dump_json()

T = TypeVar('T')

def from_json_str(json_str: str, model_class: Type[T]) -> T:
    """JSON字符串转对象
    Args:
        json_str: JSON格式字符串
        model_class: 目标类类型
    Returns:
        反序列化后的对象实例
    """
    data = json.loads(json_str)
    return model_class(**data)
