import asyncio
import uuid

from minio import Minio
import os

from minio.helpers import BaseURL

from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger


class MinIOClient:
    __instance = None

    def __new__(cls):
        if not cls.__instance:
            cls.__instance = super().__new__(cls)
            cls.__instance.__init_client()
        return cls.__instance

    def __init_client(self):
        """初始化MinIO客户端"""
        minio_config = SysConfig.get("minio", {})
        self.endpoint=minio_config.get("endpoint")
        self.secure = minio_config.get("secure", False)  # 新增实例变量存储secure配置
        self.client = Minio(
            endpoint=self.endpoint,
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            secure=self.secure
        )
        self.default_bucket = minio_config.get("bucket", "default")

    def upload_file(self, file_path: str,bucket_name: str = None, object_name: str = None) -> str:
        """
        上传文件到MinIO
        :param file_path: 本地文件路径
        :param bucket_name: 存储桶名称（可选）
        :param object_name: 对象名称（可选）
        :return: 文件访问URL
        """
        if not bucket_name:
            bucket_name = self.default_bucket

        if not object_name:
            object_name = os.path.basename(file_path)
            short_uuid = str(uuid.uuid4())[:8]
            object_name = short_uuid + "_" + object_name

        try:
            # 确保存储桶存在
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                logger.info(f"创建存储桶: {bucket_name}")

            # 执行文件上传
            self.client.fput_object(
                bucket_name=bucket_name,
                object_name=object_name,
                file_path=file_path
            )

            logger.info(f"文件上传成功: {object_name}")

            # 生成访问URL
            return self._generate_url(bucket_name, object_name)

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise Exception(f"MinIO操作失败: {str(e)}")

    def _generate_url(self, bucket_name: str, object_name: str) -> str:
        """生成文件访问URL"""
        base_url=("https://" if self.secure else "http://") + self.endpoint
        return f"{base_url}/{bucket_name}/{object_name}"
# 使用示例保持与您test.py中的调用方式一致
# MinIOClient().upload_file(res)
