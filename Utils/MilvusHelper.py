from typing import List, Optional

from langchain_milvus import MilvusCollectionHybridSearchRetriever, <PERSON><PERSON><PERSON><PERSON>
from dataclasses import dataclass, field

from langchain_milvus.utils.sparse import BM25SparseEmbedding
from pymilvus import (connections, Collection, CollectionSchema, FieldSchema,
                      DataType, utility, Function, FunctionType, AnnSearchRequest,
                      WeightedRanker)
from pymilvus.exceptions import MilvusException

from Configs.Config import SysConfig
from LLM.EmbeddingsManager import EmbeddingsManager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Utils.logs.LoggingConfig import logger


# 定义字段信息的配置类
@dataclass
class CollectionFields:
    id: str = "id"
    file_id: str = "file_id"
    file_trunk_id: str = "file_trunk_id"
    status: str = "status"
    dense_vector: str = "dense_vector"
    content: str = "content"
    metadata: str = "metadata"
    sparse_vector: str = "sparse_vector"
    keywords: str = "keywords"


# 字段配置实例
collection_fields = CollectionFields()


# 定义搜索参数配置类
@dataclass
class SearchParams:
    metric_type: str = "L2"
    nprobe: int = 10
    limit: int = 5


# 定义索引配置类
@dataclass
class IndexParams:
    dense_vector: dict = field(default_factory=lambda: {
        "metric_type": "L2",
        "index_type": "IVF_FLAT",
        "params": {"nlist": 128}
    })

    sparse_vector: dict = field(default_factory=lambda: {
        "index_type": "SPARSE_INVERTED_INDEX",
        "metric_type": "BM25",
        "params": {
            "inverted_index_algo": "DAAT_MAXSCORE",
            "bm25_k1": 1.2,
            "bm25_b": 0.75
        }
    })


index_params = IndexParams()


# 定义 MilvusHelper 类
class MilvusHelper:
    def __init__(self, embeddings_helper: EmbeddingsManager):
        # 从配置文件中获取 Milvus 连接信息
        self.config_dto = SysConfig["milvus"]
        # 初始化连接到 Milvus
        self._init_milvus_connection()
        # 获取嵌入模型
        self.__embeddings = embeddings_helper.get_embeddings()
        # 获取稀疏向量模型
        self.__sparse_embeddings = BM25SparseEmbedding
        # 获取集合
        self.__collection = self._get_collection(self.config_dto["collection_name"])

        self.__k: int = SysConfig["retrievers"]["vector"]["k"]
        self.__score_threshold: float = SysConfig["retrievers"]["vector"]["score_threshold"]
        self.__fetch_k: int = SysConfig["retrievers"]["vector"]["fetch_k"]

    def _init_milvus_connection(self):
        """初始化 Milvus 连接"""
        try:
            # 连接到 Milvus 集群
            connections.connect(
                host=self.config_dto["host"],
                port=self.config_dto["port"],
                user=self.config_dto["user"],
                password=self.config_dto["password"],
                db_name=self.config_dto["db_name"]
            )
            logger.info(f"Successfully connected to Milvus at {self.config_dto}")
            return connections
        except MilvusException as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            raise

    def _create_collection(self, collection_name: str = None) -> Collection:
        """创建或获取 Milvus 集合"""
        try:
            # 获取dim长度
            dim_length = 0
            if self.__embeddings is not None:
                # 初始化进行一次向量化获取向量长度
                dim = self.__embeddings.embed_query("获取长度")
                dim_length = len(dim)

            # 定义集合 schema
            fields = [
                FieldSchema(name=collection_fields.id, dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name=collection_fields.file_id, dtype=DataType.VARCHAR, max_length=256),
                FieldSchema(name=collection_fields.file_trunk_id, dtype=DataType.VARCHAR, max_length=256),
                FieldSchema(name=collection_fields.status, dtype=DataType.INT8),
                FieldSchema(name=collection_fields.keywords, dtype=DataType.ARRAY, element_type=DataType.VARCHAR,
                            max_capacity=1000, max_length=256, nullable=True),
                FieldSchema(
                    name=collection_fields.dense_vector,
                    dtype=DataType.FLOAT_VECTOR,
                    dim=dim_length,
                    enable_analyzer=True,
                    analyzer_params={"type": "chinese"},
                    enable_match=True
                ),
                FieldSchema(
                    name=collection_fields.content,
                    dtype=DataType.VARCHAR,
                    max_length=65535,
                    enable_analyzer=True,
                    enable_match=True
                ),  # 存储原始文本
                FieldSchema(name=collection_fields.metadata, dtype=DataType.VARCHAR, max_length=4096),
                # 稀疏向量字段（由函数自动生成）
                FieldSchema(name=collection_fields.sparse_vector, dtype=DataType.SPARSE_FLOAT_VECTOR)
            ]
            schema = CollectionSchema(fields=fields, description="Vector database collection")

            # 定义 BM25 函数，将文本转为稀疏向量
            bm25_function = Function(
                name="text_bm25_emb",
                function_type=FunctionType.BM25,
                input_field_names=[collection_fields.content],  # 输入字段为存储文本的字段
                output_field_names=[collection_fields.sparse_vector]  # 输出字段为稀疏向量字段
            )
            schema.add_function(bm25_function)

            # 创建集合
            collection = Collection(name=collection_name, schema=schema, using=self.config_dto["db_name"])

            return collection
        except MilvusException as e:
            logger.error(f"Failed to create collection: {e}")
            raise

    def _prepare_data_for_insert(self, info: KBFileTrunkInfo) -> dict:
        """准备文档数据以插入到集合中"""
        text = info.content
        return {
            collection_fields.dense_vector: self.__embeddings.embed_query(text),
            collection_fields.content: text,
            collection_fields.file_id: info.file_id,
            collection_fields.status: 0,
            collection_fields.metadata: str(info.meta_data),
            collection_fields.file_trunk_id: info.id
        }

    def _query_build_expr(self, file_ids: List[str] = None) -> Optional[str]:
        """构建 Milvus 查询表达式"""
        if file_ids is None or len(file_ids) == 0:
            return None
        # todo 考虑是否移除
        file_id_str_list = [str(file_id) for file_id in file_ids]
        return f"{collection_fields.file_id} in {file_id_str_list} and {collection_fields.status} == 0"

    def save_docs(self, trunks: List[KBFileTrunkInfo]) -> List[str]:
        """写入向量库"""
        ids = []
        collection = self.__collection
        data_list = [self._prepare_data_for_insert(info) for info in trunks]

        # todo: 创建索引处理考虑是否放在创建集合的方法
        self._create_indexes()
        # 插入数据
        mr = collection.insert(data_list)
        # 获取插入的 id
        ids.extend(mr.primary_keys)
        collection.load()

        # todo: 返回ids和trunkid做匹配返回存储到mysql
        return ids

    def _create_indexes(self):
        """创建集合索引"""
        try:
            self.__collection.create_index(
                field_name=collection_fields.dense_vector,
                index_params=index_params.dense_vector
            )
            self.__collection.create_index(
                field_name=collection_fields.sparse_vector,
                index_params=index_params.sparse_vector
            )
        except MilvusException as e:
            logger.error(f"Error creating indexes: {e}")
            raise

    def _search(self, question: str, anns_field: str, file_ids: List[str] = None, limit: int = 5):
        """通用搜索方法"""
        query_vector = self.__embeddings.embed_query(
            question) if anns_field == collection_fields.dense_vector else question
        expr = self._query_build_expr(file_ids)

        try:
            collection = self.__collection
            collection.load()

            # 设置搜索参数
            search_params = {
                "metric_type": SearchParams.metric_type,
                "params": {"nprobe": SearchParams.nprobe}
            }

            # 执行相似度搜索
            results = collection.search(
                data=[query_vector],
                anns_field=anns_field,
                param=search_params,
                expr=expr,
                limit=limit,
                output_fields=[
                    collection_fields.id,
                    collection_fields.content,
                    collection_fields.metadata,
                    collection_fields.file_trunk_id
                ]
            )
            return results
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    def search_similar(self, question: str, file_ids: List[str] = None) -> List[dict]:
        """根据查询向量进行相似度搜索"""
        results = self._search(question, collection_fields.dense_vector, file_ids, self.__k)
        return self._filter_results(results)

    def _filter_results(self, results) -> List[dict]:
        """过滤搜索结果"""
        filtered_results = []
        for res in results[0] if results else []:
            if res.distance <= self.__score_threshold:
                filtered_results.append({
                    collection_fields.id: res.id,
                    collection_fields.content: res.content,
                    collection_fields.metadata: res.metadata,
                    collection_fields.file_trunk_id: res.file_trunk_id,
                    "distance": res.distance
                })
        return filtered_results

    def hybrid_search(self, question: str, file_ids: List[str] = None) -> List[dict]:
        """执行混合搜索，同时使用向量搜索和 BM25"""
        try:
            collection = self.__collection
            collection.load()

            expr = self._query_build_expr(file_ids)

            # 创建密集向量搜索请求
            request_dense = AnnSearchRequest(
                data=[self.__embeddings.embed_query(question)],
                anns_field=collection_fields.dense_vector,
                param={"metric_type": SearchParams.metric_type, "params": {"nprobe": SearchParams.nprobe}},
                limit=SearchParams.limit,
                expr=expr
            )

            # 创建 BM25 搜索标签请求数
            request_bm25 = AnnSearchRequest(
                data=[question],
                anns_field=collection_fields.sparse_vector,
                param={"metric_type": "BM25"},
                limit=SearchParams.limit,
                expr=expr
            )

            # 合并请求
            reqs = [request_dense, request_bm25]
            # 执行混合检索
            results = collection.hybrid_search(
                reqs=reqs,
                rerank=WeightedRanker(0.6, 0.4), # todo: 做成可配置参数
                limit=SearchParams.limit,
                output_fields=[
                    collection_fields.id,
                    collection_fields.content,
                    collection_fields.metadata,
                    collection_fields.file_trunk_id
                ]
            )

            return self._filter_results(results)
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

    def delete_chunks(self, ids: List[str]) -> bool:
        """通过 ID 删除文档分块"""
        try:
            expr = f"{collection_fields.file} in {str(ids)}"
            self.__collection.delete(expr)
            return True
        except MilvusException as e:
            logger.error(f"Error during delete by file ids: {e}")
        return False

    def update_trunk(self, trunk: KBFileTrunkInfo) -> bool:
        """根据file_trunk_id更新向量集合中的指定向量"""
        try:
            collection = self.__collection
            collection.load()

            # 查询指定 trunk
            results = collection.query(
                expr=f"{collection_fields.file_trunk_id} == '{trunk.id}'",
                output_fields=self._get_available_fields()
            )
            if not results:
                logger.error(f"No data found for file_trunk_id: {trunk.id}")
                return False

            info = results[0]

            # 更新字段
            if trunk.content is not None:
                info[collection_fields.content] = trunk.content
                info[collection_fields.dense_vector] = self.__embeddings.embed_query(trunk.content)
            if trunk.meta_data is not None:
                info[collection_fields.metadata] = str(trunk.meta_data)
            if trunk.status is not None:
                info[collection_fields.status] = trunk.status

            # 执行更新操作
            collection.upsert(
                data=[info],
                primary_keys=[info[collection_fields.id]]
            )
            return True
        except MilvusException as e:
            logger.error(f"Error during update by file_trunk_id: {e}")
            return False

    def _get_available_fields(self) -> list:
        """获取可查询的字段列表"""
        return [
            field for field in [
                collection_fields.id,
                collection_fields.file_id,
                collection_fields.file_trunk_id,
                collection_fields.status,
                collection_fields.dense_vector,
                collection_fields.content,
                collection_fields.metadata
            ] if field != collection_fields.sparse_vector
        ]

    def _get_collection(self, collection_name: str = None):
        """获取集合"""
        # collection_name = self._create_valid_collection_name(collection_name)
        if utility.has_collection(collection_name):
            collection = Collection(name=collection_name)
            logger.info(f"Collection {collection_name} already exists. Loading collection.")
        else:
            # 如果集合不存在，创建集合
            collection = self._create_collection(collection_name)
            logger.info(f"Created collection {collection_name}")
        return collection

    def delete_by_file_id(self, file_id: str) -> bool:
        """通过 file_id 删除向量集合中的指定向量"""
        """通过 ID 删除文档分块"""
        try:
            expr = f"{collection_fields.file_id} == '{file_id}'"
            self.__collection.delete(expr)
            return True
        except MilvusException as e:
            logger.error(f"Error during delete by file ids: {e}")
        return False

    # 将文件名转为集合名称
    @staticmethod
    def _create_valid_collection_name(base_name: str) -> str:
        # 确保集合名称符合 Milvus 的要求
        # valid_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
        collection_name = "collection_"
        # for char in base_name:
        #     if char in valid_chars:
        #         collection_name += char
        # if len(collection_name) > 255:
        #     collection_name = collection_name[:255]
        return collection_name

    def load(self, file_id: str):
        """加载指定文件的向量"""
        try:
            # 创建密集向量搜索请求
            request_dense = {"metric_type": SearchParams.metric_type, "params": {"nprobe": SearchParams.nprobe}}
            # 创建 BM25 搜索标签请求数
            request_bm25 = {"metric_type": "BM25"}
            retriever = MilvusCollectionHybridSearchRetriever(
                collection=self.__collection,
                rerank=WeightedRanker(0.5, 0.5),
                anns_fields=[collection_fields.dense_vector, collection_fields.sparse_vector],
                field_search_params=[request_dense, request_bm25],
                field_exprs=[f"{collection_fields.file_id} == '{file_id}'"],
                top_k=self.__fetch_k,
                output_fields=[
                    collection_fields.id,
                    collection_fields.content,
                    collection_fields.metadata,
                    collection_fields.file_trunk_id
                ]
            )
            return retriever
        except MilvusException as e:
            logger.error(f"Error during load by file_id: {e}")
            return []
