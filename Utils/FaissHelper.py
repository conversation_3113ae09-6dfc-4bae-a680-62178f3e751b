import gc
import os

from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document

from Configs.Config import SysConfig
from LLM.EmbeddingsManager import EmbeddingsManager
from Utils.logs.LoggingConfig import logger


class FaissHelper:
    def __init__(self, embeddings_helper: EmbeddingsManager):

        self._save_root_path = self.__init_root_path(embeddings_helper.get_embeddings_name())

        logger.debug(f"本地向量保存根目录：{self._save_root_path}")

        self.__embeddings = embeddings_helper.get_embeddings()

        self._db_store: dict[str, FAISS] = {}

        self._max_store_count = 5  # 最大存储数量

    def __init_root_path(self, embeddings_name: str) -> str:
        faiss_root_path = SysConfig["faiss"]["save_path"]
        save_path = os.path.join(faiss_root_path, embeddings_name)

        if not os.path.exists(save_path):
            os.makedirs(save_path, exist_ok=True)
        return save_path

    def save_docs(self, documents: list[Document], index_name: str, append: bool = False) -> list[str]:
        try:
            # 创建或更新向量存储
            if index_name in self._db_store:
                logger.info(f"生成向量成功{documents}")
                return self._db_store[index_name].add_documents(documents)

            return self._create_db(documents, index_name, append)

        except Exception as e:
            logger.error(f"Error processing text blocks: {str(e)}")
            raise


    def _create_db(self, documents: list[Document], index_name: str, append: bool = False) -> list[str]:
        ids: list[str] = []

        if append and self.check_exists(index_name):
            # 加载现有向量库
            self._db = FAISS.load_local(self._save_root_path, self.__embeddings, index_name,
                                        allow_dangerous_deserialization=True)
            ids = self._db.add_documents(documents)
        else:
            self._db = FAISS.from_documents(documents=documents, embedding=self.__embeddings, ids=ids)

        self._db.save_local(self._save_root_path, index_name)

        self._db_store[index_name] = self._db

        if len(self._db_store) > self._max_store_count:
            # 删除最早的索引（字典有序，第一个是最早的）
            oldest_index = next(iter(self._db_store))
            del self._db_store[oldest_index]
            logger.info(f"已达到最大存储数量{self._max_store_count}，已移除最旧索引: {oldest_index}")

        return ids

    def save_texts(self, text_blocks: list[str], index_name: str, append: bool = False) -> list[str]:
        ids: list[str] = []
        db = None
        try:
            # 创建或更新向量存储
            if append and self.check_exists(index_name):
                # 加载现有向量库
                db = FAISS.load_local(self._save_root_path, self.__embeddings, index_name,
                                      allow_dangerous_deserialization=True)
                # 添加新的文本块
                ids = db.add_texts(text_blocks)
            else:
                # 创建新的向量库
                db = FAISS.from_texts(
                    texts=text_blocks,
                    embedding=self.__embeddings,
                    normalize_L2=True,
                    distance_strategy="COSINE",
                    ids=ids
                )

            # 保存向量库
            db.save_local(self._save_root_path, index_name)
        except Exception as e:
            logger.error(f"Error processing text blocks: {str(e)}")
            raise
        finally:
            if db is not None:
                # 清理内存
                del db
                gc.collect()

        return ids

    def load(self, index_name: str) -> FAISS | None:
        if self.check_exists(index_name):
            try:

                if index_name in self._db_store:
                    return self._db_store[index_name]

                vector_index = FAISS.load_local(self._save_root_path, self.__embeddings, index_name=index_name,
                                                allow_dangerous_deserialization=True)
                return vector_index
            except Exception as e:
                logger.error(f"Failed to load index {index_name}: {e}")
                return None
        return None

    def check_exists(self, index_name: str) -> bool:
        return os.path.exists(os.path.join(self._save_root_path, f'{index_name}.faiss'))

    def remove(self, index_name: str) -> bool:
        if not self.check_exists(index_name):
            return True

        file_path = os.path.join(self._save_root_path, f'{index_name}.faiss')
        try:
            os.remove(file_path)
            return True
        except PermissionError:
            logger.error(f"没有权限删除文件 {file_path}")
        except Exception as e:
            logger.error(f"删除文件时出错，{file_path}: {e}")

        return False

    @classmethod
    def get_index_name_by_file_id(cls, file_id: str):
        return f"file_{file_id}"
