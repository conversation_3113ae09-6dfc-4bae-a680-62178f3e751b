import logging

from Utils.logs.AbstractLogger import AbstractLogger

# 日志输出
class ConsoleLogger(AbstractLogger):
    """
    具体日志类：将日志输出到控制台
    """
    def log(self, message: str, level: int = logging.INFO) -> None:
        self.logger.log(level, message)

    def debug(self, message: str) -> None:
        self.logger.debug(message)

    def info(self, message: str) -> None:
        self.logger.info(message)

    def warning(self, message: str) -> None:
        self.logger.warning(message)

    def error(self, message: str) -> None:
        self.logger.error(message)

    def critical(self, message: str) -> None:
        self.logger.critical(message)