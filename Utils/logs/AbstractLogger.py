from abc import ABC, abstractmethod
import logging
from typing import Optional, Dict

# 日志级别及相关配置(抽象类)
class AbstractLogger(ABC):
    """
    抽象日志类，定义日志打印的配置和方法
    """
    def __init__(self, name: str, level: int = logging.INFO,
                 log_format: Optional[str] = None,
                 log_file: Optional[str] = None):
        """
        初始化日志配置
        :param name: 日志名称
        :param level: 日志级别（默认为 INFO）
        :param log_format: 日志格式（默认：'%(asctime)s - %(name)s - %(levelname)s - %(message)s'）
        :param log_file: 日志文件路径（如果为 None，则输出到控制台）
        """
        self.name = name
        self.level = level
        self.log_format = log_format or '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self.log_file = log_file

        # 配置日志
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(self.level)

        # 创建日志处理器
        if self.log_file:
            handler = logging.FileHandler(self.log_file)
        else:
            handler = logging.StreamHandler()

        # 设置日志格式
        formatter = logging.Formatter(self.log_format)
        handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(handler)

    @abstractmethod
    def log(self, message: str, level: int = logging.INFO) -> None:
        """
        抽象方法：打印日志
        :param message: 日志消息
        :param level: 日志级别
        """
        pass

    @abstractmethod
    def debug(self, message: str) -> None:
        """
        抽象方法：打印 DEBUG 级别的日志
        :param message: 日志消息
        """
        pass

    @abstractmethod
    def info(self, message: str) -> None:
        """
        抽象方法：打印 INFO 级别的日志
        :param message: 日志消息
        """
        pass

    @abstractmethod
    def warning(self, message: str) -> None:
        """
        抽象方法：打印 WARNING 级别的日志
        :param message: 日志消息
        """
        pass

    @abstractmethod
    def error(self, message: str) -> None:
        """
        抽象方法：打印 ERROR 级别的日志
        :param message: 日志消息
        """
        pass

    @abstractmethod
    def critical(self, message: str) -> None:
        """
        抽象方法：打印 CRITICAL 级别的日志
        :param message: 日志消息
        """
        pass