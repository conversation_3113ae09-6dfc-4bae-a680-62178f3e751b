# LoggingConfig.py
from loguru import logger
import sys
import os
from datetime import datetime

from Configs.Config import SysConfig
from Utils.RunningPathHelper import RunningPathHelper

# 获取项目根目录
project_root = RunningPathHelper.get_running_root_path()

# 创建日志目录
log_dir = os.path.join(project_root, str(SysConfig["log_dir"]))
print(f"日志路径：{log_dir}")
os.makedirs(log_dir, exist_ok=True)

# 配置日志记录器
# logger.remove()  # 移除默认的控制台输出

# 添加文件输出，按天旋转日志文件
# 要将所有日志类型都写到文件中，只需将日志的级别设置为最低级别（DEBUG），这样所有级别的日志（DEBUG、INFO、WARNING、ERROR、CRITICAL）都会被记录。
logger.add(
    os.path.join(log_dir, "{time:YYYY-MM-DD}.log"),
    rotation="1 day",  # 按天旋转
    retention="30 days",  # 保留30天的日志
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="DEBUG"
)

# 添加控制台输出（可选）
# logger.add(sys.stderr, format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}", level="INFO")

# 暴露 logger 给其他模块使用
__all__ = ["logger"]
