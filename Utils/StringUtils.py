import re


# 字符串工具类
class StringUtils:

    @staticmethod
    def is_empty(value):
        """判断字段是否为空或空字符串"""
        return value is None or value == ""

    @staticmethod
    def is_not_empty(value):
        """判断字段是否不为空或空字符串"""
        return not StringUtils.is_empty(value)

    @staticmethod
    def match_key_and_get_value(input_char, data_dict):
        """
        根据输入字符匹配字典中的键，并返回对应的值。
        :param input_char: 输入的字符
        :param data_dict: 包含键值对的字典
        :return: 如果找到匹配的键，返回对应的值；否则返回None
        """
        for key in data_dict.keys():
            if input_char in key:
                return data_dict[key]
        return None

    @staticmethod
    def check_ym_date_format(date_str: str):
        """
        检查日期字符串的格式是否为YYYY-MM。
        :param date_str: 日期字符串
        :return: 如果格式正确，返回 True；否则返回 False
        """
        # 年月的正则表达式：四位数字年份，两位数字月份
        ym_pattern = re.compile(r'^\d{4}-\d{2}$')
        # 检查日期字符串是否匹配年月日的格式
        if StringUtils.is_not_empty(str) and ym_pattern.match(date_str):
            return True
        else:
            return False

    @staticmethod
    def check_ymd_date_format(date_str: str):
        """
        检查日期字符串的格式是否为YYYY-MM-DD
        :param date_str: 日期字符串
        :return: 如果格式正确，返回 True；否则返回 False
        """
        # 年月日的正则表达式：四位数字年份，两位数字月份，两位数字日期
        ymd_pattern = re.compile(r'^\d{4}-\d{2}-\d{2}$')
        # 检查日期字符串是否匹配年月日的格式
        if StringUtils.is_not_empty(str) and ymd_pattern.match(date_str):
            return True
        else:
            return False
