import os
import sqlite3
import threading
from typing import Optional, List, Dict, Any

from Configs.Config import SysConfig  # 假设您有配置文件导入


class SQLiteHandler:

    def __init__(self, user_flag: str):
        """
        初始化时仅存储配置，不立即创建连接
        """
        self.db_path = SysConfig["sqlite"]["database"]
        self.db_name = f"user_chat_{user_flag}.db"
        self._full_path = os.path.join(self.db_path, self.db_name)
        self.table_name = "kb_question_chat_logs"

        # 初始化时确保目录存在
        os.makedirs(self.db_path, exist_ok=True)

        self.__conn = None
        self.__conn = self._init_conn()
        if not self.__conn:
            raise ValueError("数据库连接初始化失败")
        self.create_table()

    def _init_conn(self) -> sqlite3.Connection:
        """
        初始化当前线程专用的数据库连接（线程安全）
        """
        # 每个线程有独立的连接实例
        if self.__conn is None:
            conn = sqlite3.connect(
                self._full_path,
                check_same_thread=False  # 明确关闭线程安全检查
            )
            conn.row_factory = sqlite3.Row
            print(f"线程 {threading.get_ident()} 创建新连接")
            return conn
        return self.__conn

    def _get_conn(self) -> sqlite3.Connection:
        """
        获取当前线程专用的数据库连接（线程安全）
        """
        return self.__conn


    def execute_query(self, sql: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句（线程安全）
        """
        try:
            cn = self.__conn.cursor()
            cn.execute(sql, params or ())
            return [dict(row) for row in cn.fetchall()]
        except sqlite3.Error as e:
            self._get_conn().rollback()
            print(f"执行SQL失败: {e}\nSQL: {sql}\n参数: {params}")
            raise

    def execute_query_one(self, sql: str, params: Optional[tuple] = None) -> Optional[Dict[str, Any]]:
        """
        执行查询语句并返回单条结果（线程安全）
        """
        try:
            cn = self.__conn.cursor()
            if params:
                cn.execute(sql, params or ())
            else:
                cn.execute(sql)
            result = cn.fetchone()
            return dict(result) if result else None
        except sqlite3.Error as e:
            self._get_conn().rollback()
            print(f"执行SQL失败: {e}\nSQL: {sql}\n参数: {params}")
            raise

    def execute_non_query(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行非查询语句（线程安全）
        """
        try:
            cn = self.__conn.cursor()
            cn.execute(sql, params or ())
            self.__conn.commit()
            return cn.rowcount
        except sqlite3.Error as e:
            self._get_conn().rollback()
            print(f"执行SQL失败: {e}\nSQL: {sql}\n参数: {params}")
            raise

    def close(self) -> None:
        """
        关闭当前线程的连接（线程安全）
        """
        if hasattr(self.__conn, 'conn') and self.__conn.conn:
            self.__conn.conn.close()
            print(f"线程 {threading.get_ident()} 连接已关闭")
            # 清理线程本地存储
            self.__conn.conn = None
            self.__conn.cursor = None

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

    def table_exists(self, table_name: str) -> bool:
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_query_one(sql, (table_name,))
        return result is not None

    # 创建表结构
    def create_table(self):
        """创建数据库表"""
        try:
            if self.table_exists(self.table_name):
                return
            # 创建聊天记录表
            question_chat_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                message_id INTEGER PRIMARY KEY AUTOINCREMENT,
                conversation_id TEXT DEFAULT NULL,
                relatived_file  TEXT DEFAULT NULL,
                content TEXT DEFAULT NULL,
                message_type TEXT NOT NULL DEFAULT 'human',
                message_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            self.execute_non_query(question_chat_sql)
        except sqlite3.Error as e:
            print(f"创建表失败: {e}")
            raise
