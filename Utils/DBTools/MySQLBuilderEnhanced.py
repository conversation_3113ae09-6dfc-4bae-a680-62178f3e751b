# sql构造器
import json
from Utils.logs.LoggingConfig import logger


class MySQLBuilderEnhanced:
    @staticmethod
    def _is_empty(value, empty_values=(None, [], (), {})):
        """判断值是否为空"""
        return value in empty_values

    '''
    obj: 传入的对象，用于获取其属性。
    exclude: 要排除的属性名列表。
    skip_empty: 是否跳过空值。
    empty_values: 要跳过的空值列表。
    include_empty: 不进行空值检查的字段列表
    '''

    @staticmethod
    def _get_valid_attributes(obj, exclude=None, skip_empty=True, empty_values=(None, "", [], (), {}),
                              include_empty=None):
        """获取有效的属性名和值"""
        if exclude is None:
            exclude = []
        if include_empty is None:
            include_empty = []

        # 获取对象属性
        attributes = vars(obj)
        result = {}
        for attr, value in attributes.items():
            # 为空的、属性名以 _ 开头的私有属性和 exclude 选定的字段将被跳过
            if (not attr.startswith('_') and
                    attr not in exclude):

                # 如果字段在include_empty列表中，或者不需要跳过空值，或者值不为空，则包含该字段
                if (attr in include_empty or
                        not skip_empty or
                        not MySQLBuilderEnhanced._is_empty(value, empty_values)):

                    # 处理None值 - 直接保留None，不转换为字符串
                    if value is None:
                        result[attr] = None
                    elif isinstance(value, (str, int, float)):
                        result[attr] = value
                    elif isinstance(value, dict):
                        result[attr] = json.dumps(value)
                    else:
                        result[attr] = str(value)
        return result

    '''
    obj: 要插入的对象。
    table_name: 表名。
    exclude: 要排除的属性名列表。
    skip_empty: 是否跳过空值。
    empty_values: 要跳过的空值列表。
    include_empty: 不进行空值检查的字段列表
    '''

    @staticmethod
    def build_insert(obj, table_name, exclude=None, skip_empty=True, empty_values=(None, "", [], (), {}), symbol="%s",
                     include_empty=None):
        """构建INSERT语句"""
        valid_attrs = MySQLBuilderEnhanced._get_valid_attributes(
            obj, exclude, skip_empty, empty_values, include_empty)

        if not valid_attrs:
            raise ValueError("没有有效的属性可以插入")

        columns = list(valid_attrs.keys())
        columns_str = ', '.join(columns)
        placeholders = ', '.join([f'{symbol}'] * len(columns))
        sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        params = list(valid_attrs.values())
        return sql, params

    '''
    obj: 要更新的对象。
    table_name: 表名。
    primary_key: 主键名，默认为'id'。
    exclude: 要排除的属性名列表。
    skip_empty: 是否跳过空值。
    empty_values: 要跳过的空值列表。
    include_empty: 不进行空值检查的字段列表
    '''

    @staticmethod
    def build_batch_insert(objs, table_name, exclude=None, skip_empty=True, empty_values=(None, [], (), {}),
                           symbol="%s", include_empty=None):
        """构建批量INSERT语句"""
        if not objs:
            raise ValueError("没有对象可以插入")

        # 获取每个对象的有效属性
        valid_attrs_list = []
        for obj in objs:
            valid_attrs = MySQLBuilderEnhanced._get_valid_attributes(
                obj, exclude, skip_empty, empty_values, include_empty)
            if not valid_attrs:
                raise ValueError("没有有效的属性可以插入")
            valid_attrs_list.append(valid_attrs)

        # 确保所有对象的属性一致
        columns = list(valid_attrs_list[0].keys())
        for attrs in valid_attrs_list[1:]:
            if list(attrs.keys()) != columns:
                logger.error(f"所有对象的属性必须一致: {attrs.keys()}--{valid_attrs_list[0].keys()}")
                raise ValueError("所有对象的属性必须一致")

        # 构建SQL语句
        columns_str = ', '.join(columns)
        placeholders = ', '.join([f'{symbol}'] * len(columns))
        value_placeholders = f"({placeholders})"
        sql = f"INSERT INTO {table_name} ({columns_str}) VALUES {', '.join([value_placeholders] * len(objs))}"

        # 构建参数列表
        params = []
        for attrs in valid_attrs_list:
            params.extend(list(attrs.values()))
        return sql, params

    '''
    obj: 要更新的对象。
    table_name: 表名。
    primary_key: 主键名，默认为'id'。
    exclude: 要排除的属性名列表。
    skip_empty: 是否跳过空值。
    empty_values: 要跳过的空值列表。
    include_empty: 不进行空值检查的字段列表
    '''

    @staticmethod
    def build_update(obj, table_name, primary_key="id", exclude=None,
                     skip_empty=True, empty_values=(None, "", [], (), {}),
                     symbol="%s", include_empty=None):
        """构建UPDATE语句"""
        if exclude is None:
            exclude = []
        exclude.append(primary_key)

        valid_attrs = MySQLBuilderEnhanced._get_valid_attributes(
            obj, exclude, skip_empty, empty_values, include_empty)

        if not valid_attrs:
            raise ValueError("没有有效的属性可以更新")

        set_clause = ', '.join([f"{col}={symbol}" for col in valid_attrs.keys()])
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {primary_key} = {symbol}"
        params = list(valid_attrs.values()) + [getattr(obj, primary_key)]
        return sql, params

    '''
    obj: 参数值
    table_name: 表名
    page_num: 页码
    page_size: 每页大小
    symbol: 占位符符号，默认为%s
    include_empty: 不进行空值检查的字段列表
    Return:
    select_sql: 查询语句
    params: 参数列表
    select_total_sql: 查询总条数语句
    '''

    @staticmethod
    def build_select(obj, table_name, page_num=None, page_size=None,
                     sort_field=None, sort=None, symbol="%s", include_empty=None):
        try:
            """构建SELECT语句"""
            select_sql = f"SELECT * FROM {table_name}"
            # 拼接WHERE条件
            valid_attrs = MySQLBuilderEnhanced._get_valid_attributes(obj, include_empty=include_empty)
            params = []
            if valid_attrs:
                where_clause = " AND ".join([f"{col} = {symbol}" for col in valid_attrs.keys()])
                select_sql = f"{select_sql} WHERE {where_clause}"
                # 构建参数列表
                params.extend(list(valid_attrs.values()))

            if sort is not None and sort_field is not None:
                # 排序的值必须是静态的不允许作为参数
                select_sql = f"{select_sql} order by {sort_field} {sort}"

            print(f"预执行sql: {select_sql}, 预执行参数: {params}")
            return MySQLBuilderEnhanced.build_select_total(select_sql, params, page_num, page_size, symbol)
        except Exception as e:
            print(f"构建SELECT语句异常: {str(e)}")
            raise

    '''
    fields: 参数值
    table_name: 表名
    page_num: 页码
    page_size: 每页大小
    symbol: 占位符符号，默认为%s
    include_empty: 不进行空值检查的字段列表
    Return:
    select_sql: 查询语句
    params: 参数列表
    select_total_sql: 查询总条数语句
    '''

    @staticmethod
    def build_select_params(fields, params, table_name, page_num=None, page_size=None,
                            sort_field=None, sort=None, symbol="%s", include_empty=None):
        """构建SELECT语句"""
        try:
            select_sql = f"SELECT * FROM {table_name}"
            # 拼接WHERE条件
            if fields is not None and params is not None and len(fields) == len(params):
                where_clause = " AND ".join([f"{col} = {symbol}" for col in fields])
                select_sql = f"{select_sql} WHERE {where_clause}"
                # 构建参数列表

            if sort is not None and sort_field is not None:
                # 排序的值必须是静态的不允许作为参数
                select_sql = f"{select_sql} order by {sort_field} {sort}"

            print(f"预执行sql: {select_sql}, 预执行参数: {params}")
            return MySQLBuilderEnhanced.build_select_total(select_sql, params, page_num, page_size, symbol)
        except Exception as e:
            print(f"构建SELECT语句异常: {str(e)}")
            raise

    @staticmethod
    def build_select_total(select_sql, params, page_num=None, page_size=None, symbol="%s"):
        select_total_sql = None
        total_params = None
        # 判断是否使用分页进行查询
        if page_size is not None and page_num is not None:
            # 替换 * 为 COUNT(1)
            select_total_sql = select_sql.replace("SELECT *", "SELECT COUNT(1) total")
            # 为查询总条数sql拼接参数
            total_params = params.copy()
            # 为正常sql拼接分页
            select_sql = f"{select_sql} LIMIT {symbol} OFFSET {symbol}"
            params.append(page_size)
            params.append(page_size * (page_num - 1))

        print(f"预执行sql: {select_sql}, 预执行参数: {params}")
        return select_sql, params, select_total_sql, total_params