import threading
import time

import nacos

from Configs.Config import SysConfig


class NacosRegister:
    def __init__(self):
        nacos_config = SysConfig["nacos"]
        self.client = nacos.NacosClient(
            server_addresses=nacos_config["server_addresses"],
            namespace=nacos_config["namespace"],
            username=nacos_config["username"],
            password=nacos_config["password"]
        )
        self.service_config = nacos_config["service"]
        self._heartbeat_thread = None
        self._stop_heartbeat = False

    def _send_heartbeat(self):
        """发送心跳的后台任务"""
        while not self._stop_heartbeat:
            try:
                self.client.send_heartbeat(
                    service_name=self.service_config["service_name"],
                    ip=self.service_config["ip"],
                    port=self.service_config["port"],
                    cluster_name=self.service_config["cluster_name"],
                    group_name=self.service_config["group_name"]
                )
                time.sleep(5)  # 每5秒发送一次心跳
            except Exception as e:
                print(f"Failed to send heartbeat: {str(e)}")
                time.sleep(1)  # 发生错误时等待1秒后重试

    def register_service(self):
        """注册服务到Nacos并启动心跳"""
        try:
            self.client.add_naming_instance(
                service_name=self.service_config["service_name"],
                ip=self.service_config["ip"],
                port=self.service_config["port"],
                cluster_name=self.service_config["cluster_name"],
                group_name=self.service_config["group_name"],
                metadata=self.service_config["metadata"]
            )
            print(f"Service registered to Nacos successfully: {self.service_config['service_name']}")

            # 启动心跳线程
            self._stop_heartbeat = False
            self._heartbeat_thread = threading.Thread(target=self._send_heartbeat)
            self._heartbeat_thread.daemon = True
            self._heartbeat_thread.start()

        except Exception as e:
            print(f"Failed to register service to Nacos: {str(e)}")

    def deregister_service(self):
        """从Nacos注销服务并停止心跳"""
        # 首先停止心跳
        self._stop_heartbeat = True
        if self._heartbeat_thread:
            self._heartbeat_thread.join(timeout=1)

        try:
            self.client.remove_naming_instance(
                service_name=self.service_config["service_name"],
                ip=self.service_config["ip"],
                port=self.service_config["port"],
                cluster_name=self.service_config["cluster_name"],
                group_name=self.service_config["group_name"]
            )
            print(f"Service deregistered from Nacos successfully: {self.service_config['service_name']}")
        except Exception as e:
            print(f"Failed to deregister service from Nacos: {str(e)}")
