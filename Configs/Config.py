import json
import os

from Utils.RunningPathHelper import RunningPathHelper


def load_config():
    # 获取当前文件所在目录的绝对路径
    current_dir = RunningPathHelper.get_running_root_path()

    env_file = os.path.join(current_dir, "Configs", "env.json")
    config_file_name = "config.json"
    if os.path.exists(env_file):
        try:
            with open(env_file, "r", errors="ignore") as ef:
                content = ef.read()
                config = json.loads(content)
                env_value = config['environment']
                if env_value:
                    config_file_name = f"config_{env_value}.json"
        except IOError:
            print("读取env.json文件错误")

    file = os.path.join(current_dir, "Configs", config_file_name)  # 直接在同目录下查找
    with open(file, "r", errors="ignore") as f:
        content = f.read()
        current_ip = os.getenv('HOST_IP')
        if current_ip:
            content = content.replace("#localhost#", current_ip)
            print(f"获取到的宿主机 IP 地址是: {current_ip}")
        else:
            print("未获取到宿主机 IP 地址。")

        config = json.loads(content)

    return config


SysConfig = load_config()
