from Models.pojo.KBFileInfo import KBFileInfo
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorSaveService
from Services.SqlServer.KBFileInfoService import K<PERSON>ileInfoService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Services.TaskServer.BaseGenTask import BaseGenTask, GenTaskState
from Utils.logs.LoggingConfig import logger


class KBFileDeleteGenTask(BaseGenTask):

    # 初始化
    def __init__(self, file_id: str):
        super().__init__(None)
        self._file_id: str = file_id
        self._kb_file_info_service = KBFileInfoService()
        self._kb_file_trunk_service = KBFileTrunkInfoService()

    async def _task(self):
        # 开始任务
        try:
            # 先删除向量化数据
            TrunkVectorSaveService.delete_by_file_id(self._file_id)
            # 删除块数据
            self._kb_file_trunk_service.batch_delete(file_ids=self._file_id)
            # 删除文件信息
            self._kb_file_info_service.batch_real_delete(file_ids=self._file_id)
        except Exception as e:
            # 删除失败
            self._kb_file_info_service.update(KBFileInfo(file_id=self._file_id, del_flag=2))
            logger.error(f"error msg : {e}")
        return

    def _update_task_state(self, state: GenTaskState, result=None):
        pass
