from typing import List, Dict

from Configs.Config import SysConfig
from Services.TaskServer.BaseGenTask import BaseGenTask, GenTaskState
from Services.TaskServer.BaseTaskWorker import Base<PERSON>askWorker
from Services.TaskServer.TaskFactory import TaskFactory
from Utils.logs.LoggingConfig import logger


class KBFileVectorTaskWorker(BaseTaskWorker):
    def __init__(self, interval: int = 5):
        super().__init__(interval)
        # 并发数量
        self.__running_task_count = SysConfig["vector_gen"]["concurrency"]
        self.__running_tasks: Dict[str, BaseGenTask] = {}

    def _do_work(self) -> None:
        task_need_count = self.__running_task_count - len(self.__running_tasks)
        if task_need_count > 0:
            task_infos = TaskFactory.query_for_tasks(task_need_count)
            task_infos = task_infos[:task_need_count]
            for task_data in task_infos:
                task_id = task_data.task_id
                if task_id not in self.__running_tasks.keys():
                    task = TaskFactory.create_task(task_data)
                    if task:
                        self.__running_tasks[task_id] = task
                        task_need_count -= 1
                if task_need_count <= 0:
                    break

        # todo 失败任务我在考虑考虑怎么实现 --王鹏
        # self.__recover_failed_tasks()

        # 执行下载和解析任务
        finished_task_ids: List[str] = []
        for task_id in self.__running_tasks.keys():
            task = self.__running_tasks[task_id]
            if task.state == GenTaskState.Finished:
                finished_task_ids.append(task_id)
            elif task.state == GenTaskState.Stopped:
                task.start_downloading()
            elif task.state == GenTaskState.Downloaded:
                task.start_generation()

        # 循环从执行中移除这个任务
        for task_id in finished_task_ids:
            del self.__running_tasks[task_id]
            logger.info(f"Task {task_id} finished.")
