import asyncio
import os
import threading
from abc import ABC, abstractmethod
from enum import Enum
from typing import Union

from Configs.Config import SysConfig
from Utils.FileDownloader import FileDownloader
from Utils.logs.LoggingConfig import logger


# 工作状态
class GenTaskState(Enum):
    Stopped = 0,
    Downloading = 1,
    Downloaded = 2,
    Generating = 3,
    Finished = 4
    FAILED = 5


class BaseGenTask(ABC):
    # 初始化
    def __init__(self, file_url: str=None):
        self._last_result: int = 0
        self._file_url: str = file_url
        self._local_temp_path: str = ""
        self._state: GenTaskState = GenTaskState.Stopped
        # 基础配置
        self.temp_dir = SysConfig["vector_gen"]["temp_dir"]
        # 服务实例
        self.file_downloader = FileDownloader(self.temp_dir)

        self._download_thread = threading.Thread(target=self.__download_file)
        self._work_thread = threading.Thread(target=self.__process_file)

    @abstractmethod
    async def _task(self):
        # 运行方法中要加 await
        pass

    @abstractmethod
    def _update_task_state(self, state: GenTaskState, result=None):
        # 更新状态，可处理失败情况，和更新任务状态
        pass

    @property
    def file_url(self) -> str:
        return self._file_url

    @property
    def state(self) -> GenTaskState:
        return self._state

    # 下载文件
    def start_downloading(self):
        # 校验当前状态是否为未开始
        if self.state != GenTaskState.Stopped or self._download_thread.is_alive():
            return
        # 启动下载线程
        self._download_thread.start()

    def __download_file(self):
        try:
            if not self._file_url:
                self._state = GenTaskState.Downloaded
                return

            logger.info(f"开始下载文件 {self._file_url}")
            # 下载中
            self._state = GenTaskState.Downloading
            # 下载文件到临时目录
            self._local_temp_path = self.file_downloader.download(self._file_url)
            logger.info(f"下载文件到指定目录 {self._local_temp_path}")
            # 判断是否存有临时下载目录
            if not self._local_temp_path:
                logger.info(f"下载文件到指定目录失败 {self._local_temp_path}")
                raise Exception("下载文件失败")
            # 下载完成更新状态
            self._state = GenTaskState.Downloaded
            logger.info(f"下载完成更新状态 {self._state}")
            # 通知api更新
            self._update_task_state(GenTaskState.Downloaded)
        except Exception as e:
            logger.error(f"下载文件失败 {self._file_url}: {e}")
            # 更新文件状态更新失败
            self._update_task_state(GenTaskState.FAILED)
            # 更新对象状态为已完成
            self._state = GenTaskState.Finished

    # 下载完成后，开始生成向量
    def start_generation(self):
        if self._state != GenTaskState.Downloaded or self._work_thread.is_alive():
            return
        # 启动解析线程
        self._work_thread.start()

    # 执行任务
    def __process_file(self):
        """处理单个文件"""
        logger.info("进入文件处理流程方法：__process_file")
        self._state = GenTaskState.Generating

        try:
            if self._file_url:
                if not self._local_temp_path:
                    logger.error("未能找到相关文件，无法进行处理")
                    raise Exception("Failed to download file")

                # 验证文件是否存在和可读
                if not os.path.exists(self._local_temp_path):
                    logger.error(f"文件不可读或不存在: {self._local_temp_path}")
                    raise FileNotFoundError(f"Downloaded file not found: {self._local_temp_path}")

                # 检查文件大小
                file_size = os.path.getsize(self._local_temp_path)
                logger.info(f"Downloaded file size: {file_size} bytes")

            # 开启一个异步任务
            trunk_count = asyncio.run(self._task())

            if isinstance(trunk_count, int):
                # 更新状态
                self._update_task_state(GenTaskState.Finished, trunk_count)

        except Exception as e:
            logger.error(f"任务失败: {str(e)}")
            self._update_task_state(GenTaskState.FAILED)
        finally:
            # 更新对象状态为已完成
            self._state = GenTaskState.Finished

            # 清理临时文件
            if self._local_temp_path and os.path.exists(self._local_temp_path):
                try:
                    os.remove(self._local_temp_path)
                    logger.info(f"Temporary file removed: {self._local_temp_path}")
                except Exception as e:
                    logger.error(f"Error removing temporary file: {str(e)}")

    # 缓存文件
    # def to_cache(self):
    #     # 缓存json格式到本地文件中
    #     kb_task_cache = {
    #         "kb_id": self._kb_id,
    #         "file_id": self._file_id,
    #         "file_url": self._file_url,
    #         "state": self._state.value,
    #         "result": self._last_result
    #     }
    #     # 写之前校验一下是不是存有文件
    #     with open(f"{self._file_id}.json", "w") as f:
    #         # 写入文件
    #         json.dump(kb_task_cache, f)

    # 从本地文件中加载
    # @classmethod
    # def from_cache(cls):
    #     task_list: List[KBFileGenTask] = []
    #     cache_dir = SysConfig["vector_gen"]["cache_dir"]
    #     # 校验地址是否为null
    #     if not cache_dir:
    #         return task_list
    #     # 校验目录是否存在
    #     if not os.path.exists(cache_dir):
    #         # 创建目录
    #         os.makedirs(cache_dir)
    #
    #     for filename in os.listdir(cache_dir):
    #         if filename.endswith('.json'):
    #             with open(filename, "r") as f:
    #                 data = json.load(f)
    #                 task = KBFileGenTask(data["kb_id"], data["file_id"], data["file_url"])
    #                 task._state = KBFileTaskState(data["state"])
    #                 task._last_result = data["result"]
    #                 task_list.append(task)
    #
    #     return task_list

    # # 恢复失败的任务
    # def recover(self) -> bool:
    #     # 执行update
    #     self.__update_file_generate_state(FileState.UNPROCESSED)
    #     # 获取文件内容
    #     cache_dir = SysConfig["vector_gen"]["cache_dir"]
    #     # 校验文件是否存在
    #     json_file = os.path.join(cache_dir, f"{self._file_id}.json")
    #     if not os.path.exists(json_file):
    #         return False
    #     # 存在则删除
    #     os.remove(json_file)
    #     return True
