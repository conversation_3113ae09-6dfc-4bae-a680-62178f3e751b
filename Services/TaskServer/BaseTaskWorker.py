from Utils.logs.LoggingConfig import logger
import time
from abc import ABC, abstractmethod
import threading
from datetime import datetime, timedelta


class BaseTaskWorker(ABC):
    @abstractmethod
    def __init__(self, interval: int):
        self.__is_running: bool = False
        self.__interval: int = interval

    # properties
    @property
    def is_running(self):
        return self.__is_running

    @property
    def interval(self):
        return self.__interval

    @interval.setter
    def interval(self, value: int):
        if value <= 0:
            raise ValueError("interval必须设置大于0的整数")
        self.__interval = value

    def start(self) -> None:
        if not self.__is_running:
            self.__is_running = True
            threading.Thread(target=self.__work, daemon=True).start()

            self._on_started()

    def stop(self) -> None:
        self.__is_running = False

    def __work(self) -> None:
        last_work_time = datetime.now()
        while self.is_running:
            now = datetime.now()

            try:
                if last_work_time + timedelta(seconds=self.interval) < now:
                    self._do_work()
                    last_work_time = now
            except Exception as e:
                logger.error(f'BaseTaskWorker working error:{e}')
            finally:
                time.sleep(0.5)
        # 执行结束动作
        self._on_stopped()

    @abstractmethod
    def _do_work(self) -> None:
        pass

    def _on_started(self) -> None:
        pass

    def _on_stopped(self) -> None:
        pass
