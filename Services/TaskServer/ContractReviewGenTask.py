from Agents.AnalysisFactory import AnalysisFactory
from Models.peewee.OrmModel import TaskType, TaskState
from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
from Services.TaskServer.BaseGenTask import BaseGenTask, GenTaskState


class ContractReviewGenTask(BaseGenTask):
    # 初始化
    def __init__(self, work_id: int, file_url: str, task_type:TaskType):
        super().__init__(file_url)
        self.agent = AnalysisFactory.get_analysis_agent(task_type)
        self._work_id = work_id

    async def _task(self):
        # 开始任务
        return await self.agent.run(work_id=self._work_id, file_path=self._local_temp_path)

    def _update_task_state(self, state: GenTaskState, result=0):
        # 更新状态，可处理失败情况，和更新任务状态
        if state == GenTaskState.FAILED:
            KBAnalysisTaskServer.update_state(self._work_id, TaskState.FAILED)
        elif state == GenTaskState.Downloaded:
            # 更新状态
            KBAnalysisTaskServer.update_state(self._work_id, TaskState.DOWNLOADED)


