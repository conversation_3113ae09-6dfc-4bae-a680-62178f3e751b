from typing import List, Optional

from pydantic import BaseModel

from Models.FileState import FileState
from Models.peewee.OrmModel import KBAnalysisTask, TaskState, TaskType
from Models.pojo.KBFileInfo import K<PERSON>ileInfo
from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Services.TaskServer.BaseGenTask import BaseGenTask
from Services.TaskServer.ContractReviewGenTask import ContractReviewGenTask
from Services.TaskServer.KBFileDeleteGenTask import K<PERSON><PERSON>D<PERSON>teGenTask
from Services.TaskServer.KBFileGenTask import K<PERSON><PERSON>GenTask
from Utils.logs.LoggingConfig import logger


class TaskEnty(BaseModel):
    task_id: str  # 任务ID,表名+_+id

    # 分拣分块需要的参数
    kb_id: Optional[int] = None
    file_id: Optional[str] = None
    file_url: Optional[str] = None

    # 审核agent 需要的参数
    group_id: Optional[int] = None
    work_id: Optional[int] = None
    task_type: Optional[TaskType] = None


class TaskFactory:

    @staticmethod
    def create_task(task_data: TaskEnty) -> BaseGenTask | None:
        try:
            if task_data.task_id.startswith("KBFileInfo_"):
                return KBFileGenTask(kb_id=task_data.kb_id, file_id=task_data.file_id, file_url=task_data.file_url)
            elif task_data.task_id.startswith("KBAnalysisTask_"):
                return ContractReviewGenTask(work_id=task_data.work_id, file_url=task_data.file_url,task_type=task_data.task_type)
            elif task_data.task_id.startswith("KBFileInfoDelete_"):
                return KBFileDeleteGenTask(file_id=task_data.file_id)
            return None
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def query_for_tasks(max_count: int = 10) -> List[TaskEnty]:
        try:
            # 查询待解析文件列表
            transform_row_list = []
            # 可增加任务查询
            TaskFactory.__query_tasks_by_kb_file_info_service(transform_row_list, max_count)
            # 审查任务
            TaskFactory.__query_tasks_by_kb_analysis_task_service(transform_row_list, max_count)

            return transform_row_list
        except Exception as e:
            logger.error(f"Error getting unprocessed files: {str(e)}", exc_info=True)
            return []

    @staticmethod
    def __query_tasks_by_kb_file_info_service(transform_row_list: List[TaskEnty], max_count: int = 10):
        # 查询待解析文件列表
        row_list, total = KBFileInfoService().select_all(KBFileInfo(file_state=FileState.UNPROCESSED), page_num=1,page_size=max_count)
        #文件解析任务
        if total > 0:
            for row in row_list:
                transform_row_list.append(
                    TaskEnty(task_id=f"KBFileInfo_{row.file_id}", kb_id=row.kb_id, file_id=row.file_id,file_url=row.content))

        #增加一个删除任务
        row_list, total = KBFileInfoService().select_all(KBFileInfo(del_flag=1), page_num=1,page_size=max_count)

        # 文件解析任务
        if total > 0:
            for row in row_list:
                transform_row_list.append(
                    TaskEnty(task_id=f"KBFileInfoDelete_{row.file_id}",file_id=row.file_id))


    @staticmethod
    def __query_tasks_by_kb_analysis_task_service(transform_row_list: List[TaskEnty], max_count: int = 10):
        # 查询待解析文件列表
        row_list, total = KBAnalysisTaskServer.query(KBAnalysisTask.state_equals(TaskState.WAITING), page=1,
                                                     page_size=max_count)
        if len(row_list) > 0:
            for row in row_list:
                transform_row_list.append(
                    TaskEnty(task_id=f"KBAnalysisTask_{row.id}", work_id=row.id, file_url=row.file_url,
                             task_type=row.task_type))

        # 清除未处理完的文件

    @staticmethod
    def clean_task():
        TaskFactory._clean_file_task()
        TaskFactory._clean_analysis_task()


    @staticmethod
    def _clean_file_task():
        #改状态就可以了
        file_service = KBFileInfoService()
        row_list, total = file_service.select_all(KBFileInfo(file_state=FileState.PROCESSING), page_num=1,page_size=1000)
        for row in row_list:
            row.file_state = FileState.UNPROCESSED
            file_service.update(row)


    @staticmethod
    def _clean_analysis_task():
        row_list, total = KBAnalysisTaskServer.query(KBAnalysisTask.state_equals(TaskState.PROCESSING), page=1,
                                                     page_size=100)
        for row in row_list:
            KBAnalysisTaskServer.update_state(row.id, TaskState.WAITING)
