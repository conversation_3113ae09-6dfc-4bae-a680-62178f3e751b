from Models.FileState import FileState
from Models.pojo.KBFileInfo import KBFileInfo
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorSaveService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Services.TaskServer.BaseGenTask import BaseGenTask, GenTaskState
from Services.VectorGenService import VectorGenService
from Utils.logs.LoggingConfig import logger


class KBFileGenTask(BaseGenTask):
    # 初始化
    def __init__(self, kb_id: int, file_id: str, file_url: str):
        super().__init__(file_url)
        self._file_id: str = file_id
        self._kb_id: int = kb_id
        self._kb_file_info = KBFileInfoService()
        self._kb_file_trunk_service = KBFileTrunkInfoService()

    async def _task(self):
        # 开始任务
        return await VectorGenService().gen_vectors(self._kb_id, self._file_id, self._local_temp_path)

    def _update_task_state(self, state: GenTaskState, result=0):
        # 更新状态，可处理失败情况，和更新任务状态
        if state == GenTaskState.Finished:
            self.__update_file_generate_state(FileState.COMPLETED, trunk_count=result)
        elif state== GenTaskState.FAILED:
            self.__update_file_generate_state(FileState.FAILED)
            # 删除文件分段
            self._kb_file_trunk_service.batch_delete(file_ids=self._file_id)
            # 删除向量分段
            TrunkVectorSaveService.delete_by_file_id(self._file_id)
        elif state == GenTaskState.Downloaded:
            self.__update_file_generate_state(FileState.PROCESSING)


    def __update_file_generate_state(self, result: int, trunk_count: int = 0):
        try:
            # 更新状态
            self._last_result = result
            # 更新数据库状态
            file_info = KBFileInfo(file_id=self._file_id, kb_id=self._kb_id, file_state=result, trunk_count=trunk_count)
            self._kb_file_info.update(file_info)
            return
        except Exception as e:
            logger.error(f"error msg : {e}")


