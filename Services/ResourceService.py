import os
import os
import threading

from Models.pojo.KBFileInfo import KBFileInfo
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Utils.MinIOClient import MinIO<PERSON>lient
from Utils.RunningPathHelper import RunningPathHelper
from Utils.logs.LoggingConfig import logger
PROJECT_ROOT = RunningPathHelper.get_running_root_path()

INTER_LAW_KNOWLEDGE_ID = 900000
INTER_LAW_KNOWLEDGE_DIR = "Resource/Law"


class ResourceService:
    # 内部法律知识库id
    @staticmethod
    def get_absolute_path(relative_path: str) -> str:
        """将相对路径转换为基于项目根目录的绝对路径"""
        return os.path.join(PROJECT_ROOT, relative_path)

    @staticmethod
    def get_file_name_list(relative_path: str):
        """将相对路径转换为基于项目根目录的绝对路径"""
        directory = ResourceService.get_absolute_path(relative_path)
        if not os.path.exists(directory):
            raise FileNotFoundError(f"目录不存在: {directory}")
        if not os.path.isdir(directory):
            raise NotADirectoryError(f"路径不是目录: {directory}")
        try:
            all_files = [f for f in os.listdir(directory)
                         if os.path.isfile(os.path.join(directory, f))]
            return all_files
        except Exception as e:
            raise Exception(f"获取文件列表失败: {str(e)}")

    @staticmethod
    def get_file_size(file_path: str) -> int:
        """获取文件大小（字节）"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        if not os.path.isfile(file_path):
            raise ValueError(f"路径不是文件: {file_path}")
        return os.path.getsize(file_path)

    @staticmethod
    def get_file_list(kb_id: int) -> list[KBFileInfo]:
        # 获取内部知识库文件
        row_list, total = KBFileInfoService().select_all(KBFileInfo(kb_id=kb_id), page_num=1, page_size=1000)
        return row_list

    # 初始化法律知识库资源
    @staticmethod
    def init_resource():
        # 初始化法律相关知识
        threading.Thread(target=ResourceService._as_init_resource).start()

    @staticmethod
    def _as_init_resource():
        import nltk
        from Utils.CommonUtils import get_root_file_path
        path = get_root_file_path("data").join("nltk_data")
        nltk.data.path.append(path)
        # import tiktoken
        # enc = tiktoken.encoding_for_model("gpt2")
        # res = len(enc.encode("下载完成"))
        #logger.info(f"nltk加载，tiktoken加载完成{res}")
        ResourceService._init_resource(INTER_LAW_KNOWLEDGE_ID, INTER_LAW_KNOWLEDGE_DIR)

    @staticmethod
    def _init_resource(kb_id: int, dir_path: str):
        # 获取内部知识库文件
        row_list = ResourceService.get_file_list(kb_id)

        law_file_name_list = ResourceService.get_file_name_list(dir_path)
        # 判断文件是否在知识库内
        for file_name in law_file_name_list:
            file_exists = any(row.file_name == file_name for row in row_list)
            if not file_exists:
                # 构建相对路径
                relative_path = os.path.join(dir_path, file_name)
                file_path = ResourceService.get_absolute_path(relative_path)
                # 上传
                file_url = MinIOClient().upload_file(file_path)
                size = ResourceService.get_file_size(file_path)
                # 插入数据库
                kb = KBFileInfo(kb_id=kb_id, file_name=file_name, content=file_url, file_size=size)
                KBFileInfoService().insert(kb)
