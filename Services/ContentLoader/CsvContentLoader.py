import csv
import re
from io import <PERSON><PERSON><PERSON>rapper
from pathlib import Path
from typing import Dict, Iterator, Optional, Sequence, Union
from langchain_community.document_loaders.base import BaseLoader
from langchain_community.document_loaders.helpers import detect_file_encodings
from langchain_core.documents import Document
from .BaseContentLoader import BaseContentLoader


class MyCSVLoader(BaseLoader):

    def __init__(
            self,
            file_path: Union[str, Path],
            source_column: Optional[str] = None,
            metadata_columns: Sequence[str] = (),
            csv_args: Optional[Dict] = None,
            encoding: Optional[str] = None,
            autodetect_encoding: bool = False,
            *,
            content_columns: Sequence[str] = (),
    ):

        self.file_path = file_path
        self.source_column = source_column
        self.metadata_columns = metadata_columns
        self.encoding = encoding
        self.csv_args = csv_args or {}
        self.autodetect_encoding = autodetect_encoding
        self.content_columns = content_columns

    def lazy_load(self) -> Iterator[Document]:
        try:
            with open(self.file_path, newline="", encoding=self.encoding) as csvfile:
                yield from self.__read_file(csvfile)
        except UnicodeDecodeError as e:
            if self.autodetect_encoding:
                detected_encodings = detect_file_encodings(self.file_path)
                for encoding in detected_encodings:
                    try:
                        with open(
                                self.file_path, newline="", encoding=encoding.encoding
                        ) as csvfile:
                            yield from self.__read_file(csvfile)
                            break
                    except UnicodeDecodeError:
                        continue
            else:
                raise RuntimeError(f"Error loading {self.file_path}") from e
        except Exception as e:
            raise RuntimeError(f"Error loading {self.file_path}") from e

    def __clean_document_content(self,content: str) -> str:
        """使用正则表达式清理文档内容，去除字段前缀"""
        # 匹配任意字段名后跟冒号的模式（如 title:, content: 等）
        pattern = r'^[a-zA-Z_]+:\s*'
        # 替换掉匹配到的前缀
        return re.sub(pattern, '', content, flags=re.MULTILINE)

    def __read_file(self, csvfile: TextIOWrapper) -> Iterator[Document]:
        csv_reader = csv.DictReader(csvfile, **self.csv_args)
        for i, row in enumerate(csv_reader):
            try:
                source = (
                    row[self.source_column]
                    if self.source_column is not None
                    else str(self.file_path)
                )
            except KeyError:
                raise ValueError(
                    f"Source column '{self.source_column}' not found in CSV file."
                )
            content = "\n".join(
                f"""{k.strip() if k is not None else k}: {
                v.strip()
                if isinstance(v, str)
                else ",".join(map(str.strip, v))
                if isinstance(v, list)
                else v
                }"""
                for k, v in row.items()
                if (
                    k in self.content_columns
                    if self.content_columns
                    else k not in self.metadata_columns
                )
            )
            metadata = {"source": source, "row": i}
            for col in self.metadata_columns:
                try:
                    metadata[col] = row[col]
                except KeyError:
                    raise ValueError(f"Metadata column '{col}' not found in CSV file.")
            content=self.__clean_document_content(content)
            yield Document(page_content=content, metadata=metadata)


class CsvContentLoader(BaseContentLoader):

    def __init__(self, content: str,chunk_size:int=400):
        super().__init__(content,chunk_size)

    def _is_use_common_splitter(self) -> bool:
        return False

    def _get_loader(self) -> BaseLoader:
        #return UnstructuredCSVLoader(self._content, mode="single")
        csv_args = {
            'delimiter': ',',  # CSV 分隔符，通常为逗号
            'quotechar': '"',  # 引用字符，通常为双引号
            'skipinitialspace': True,  # 跳过字段值前后的空白字符（可选）
        }

        return MyCSVLoader(self._content,encoding='utf-8',autodetect_encoding=True,csv_args=csv_args)
