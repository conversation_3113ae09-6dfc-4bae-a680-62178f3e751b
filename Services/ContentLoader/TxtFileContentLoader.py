from langchain_community.document_loaders import TextLoader
from langchain_core.document_loaders import BaseLoader
from .BaseContentLoader import BaseContentLoader


class TxtFileContentLoader(BaseContentLoader):

    def __init__(self, content: str, chunk_size: int = 400):
        super().__init__(content, chunk_size)

    def _get_loader(self) -> BaseLoader:
        return TextLoader(self._content, autodetect_encoding=True)
