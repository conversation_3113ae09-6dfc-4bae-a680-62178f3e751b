from langchain_community.document_loaders import PyPDFLoader, UnstructuredPDFLoader
from langchain_core.document_loaders import BaseLoader

from .BaseContentLoader import BaseContentLoader


class PDFContentLoader(BaseContentLoader):
    def __init__(self, content: str, chunk_size: int = 400):
        super().__init__(content, chunk_size)

    def _get_loader(self) -> BaseLoader:
        return PyPDFLoader(self._content, mode='page')
