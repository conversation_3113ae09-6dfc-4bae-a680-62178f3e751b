from langchain_community.document_loaders import UnstructuredExcelLoader
from langchain_core.document_loaders import BaseLoader
import pandas as pd
from .BaseContentLoader import BaseContentLoader


class ExcelContentLoader(BaseContentLoader):

    def __init__(self, content: str, chunk_size: int = 400):
        super().__init__(content, chunk_size)

    def _get_loader(self) -> BaseLoader:
        # todo: 据说此方式不会加载公式，需配合pandas转换文档后进行解析
        return UnstructuredExcelLoader(self._content, mode="single")
