from langchain_community.document_loaders import UnstructuredWordDocumentLoader
from langchain_core.document_loaders import BaseLoader
from .BaseContentLoader import BaseContentLoader


class WordDocContentLoader(BaseContentLoader):

    def __init__(self, content: str, chunk_size: int = 400):
        super().__init__(content, chunk_size)

    def _get_loader(self) -> BaseLoader:
        return UnstructuredWordDocumentLoader(self._content, mode="single")
