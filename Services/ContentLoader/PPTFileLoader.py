from langchain_community.document_loaders import UnstructuredPowerPointLoader
from langchain_core.document_loaders import BaseLoader

from Services.ContentLoader.BaseContentLoader import BaseContentLoader


class PPTFileLoader(BaseContentLoader):
    def __init__(self, content: str, chunk_size: int = 400):
        super().__init__(content, chunk_size)

    def _get_loader(self) -> BaseLoader:
        return UnstructuredPowerPointLoader(self._content, mode="single")
