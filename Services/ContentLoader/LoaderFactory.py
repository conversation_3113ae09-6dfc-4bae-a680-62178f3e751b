import os

from Services.ContentLoader.BaseContentLoader import BaseContentLoader
from Services.ContentLoader.CsvContentLoader import CsvContentLoader
from Services.ContentLoader.ExcelContentLoader import ExcelContentLoader
from Services.ContentLoader.HtmlFileLoader import HtmlFileLoader
from Services.ContentLoader.MdFileContentLoader import MdFileContentLoader
from Services.ContentLoader.PDFContentLoader import PDFContentLoader
from Services.ContentLoader.PPTFileLoader import PPTFileLoader
from Services.ContentLoader.TxtFileContentLoader import TxtFileContentLoader
from Services.ContentLoader.WordDocContentLoader import WordDocContentLoader


class LoaderFactory:

    # 获取文件加载器
    @staticmethod
    def get_file_loader(file_path: str, chunk_size: int = 400) -> BaseContentLoader | None:
        file_extension = os.path.splitext(file_path)[1].lower()[1:]  # 移除点号
        if file_extension == 'doc' or file_extension == 'docx':
            return WordDocContentLoader(file_path, chunk_size)
        elif file_extension == 'pdf':
            return PDFContentLoader(file_path, chunk_size)
        elif file_extension == 'xls' or file_extension == 'xlsx':
            return ExcelContentLoader(file_path, chunk_size)
        elif file_extension == 'txt':
            return TxtFileContentLoader(file_path, chunk_size)
        elif file_extension == 'md':
            return MdFileContentLoader(file_path, chunk_size)
        elif file_extension == 'csv':
            return CsvContentLoader(file_path, chunk_size)
        elif file_extension == 'pptx' or file_extension == 'ppt':
            return PPTFileLoader(file_path, chunk_size)
        elif file_extension == 'html':
            return HtmlFileLoader(file_path, chunk_size)
        return None
