import ast
from dataclasses import dataclass, field
from typing import List, Optional

from langchain_core.documents import Document
from langchain_milvus.utils.sparse import BM25SparseEmbedding
from pymilvus import (connections, Collection, CollectionSchema, FieldSchema,
                      DataType, utility, Function, FunctionType, AnnSearchRequest,
                      WeightedRanker, MilvusException)

from Configs.Config import SysConfig
from LLM.EmbeddingsManager import EmbeddingsManager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.KBTrunkVectorService.KBTrunkVectorBaseService import KBTrunkVectorUpdateService
from Utils.CommonUtils import transform_to_document
from Utils.logs.LoggingConfig import logger
from Services.KBTrunkVectorService.CustomBM25Retriever import query_bm25_retriever

@dataclass
class CollectionFields:
    id: str = "id"
    file_id: str = "file_id"
    file_trunk_id: str = "file_trunk_id"
    status: str = "status"
    dense_vector: str = "dense_vector"
    content: str = "content"
    metadata: str = "metadata"
    sparse_vector: str = "sparse_vector"
    keywords: str = "keywords"


@dataclass
class SearchParams:
    metric_type: str = "L2"
    nprobe: int = 10
    limit: int = 5


# 定义索引配置类
@dataclass
class IndexParams:
    dense_vector: dict = field(default_factory=lambda: {
        "metric_type": "L2",
        "index_type": "IVF_FLAT",
        "params": {"nlist": 128}
    })

    sparse_vector: dict = field(default_factory=lambda: {
        "index_type": "SPARSE_INVERTED_INDEX",
        "metric_type": "BM25",
        "params": {
            "inverted_index_algo": "DAAT_MAXSCORE",
            "bm25_k1": 1.2,
            "bm25_b": 0.75
        }
    })


index_params = IndexParams()


class MilvusTrunkVectorService(KBTrunkVectorUpdateService):
    def __init__(self, embeddings_helper: EmbeddingsManager):
        super().__init__(embeddings_helper)

        # 获取稀疏向量模型
        self.__sparse_embeddings = BM25SparseEmbedding

        # 从配置文件中获取 Milvus 连接信息
        self.__config_dto = SysConfig["milvus"]
        self.__collection_name = self.__config_dto["collection_name"]
        self.__db_name = self.__config_dto["db_name"]

        self.__k: int = SysConfig["retrievers"]["vector"]["k"]
        self.__score_threshold: float = SysConfig["retrievers"]["vector"]["score_threshold"]
        self.__fetch_k: int = SysConfig["retrievers"]["vector"]["fetch_k"]
        self.__bm25_weight: float = SysConfig["retrievers"]["ensemble"]["bm25_weight"]


    def _init_milvus_connection(self, config_dto) -> bool:
        """初始化 Milvus 连接"""
        try:
            # 连接到 Milvus 集群
            connections.connect(
                host=config_dto["host"],
                port=config_dto["port"],
                user=config_dto["user"],
                password=config_dto["password"],
                db_name=config_dto["db_name"]
            )
            logger.info(f"Successfully connected to Milvus at {config_dto}")
            return True
        except MilvusException as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            return False



    def _get_collection(self, collection_name: str = None) -> Collection | None:
        """获取集合"""
        if collection_name is None:
            collection_name = self.__collection_name
        if utility.has_collection(collection_name):
            collection = Collection(name=collection_name)
            logger.info(f"Collection {collection_name} already exists. Loading collection.")
        else:
            # 如果集合不存在，创建集合
            collection = self._create_collection(collection_name)
            logger.info(f"Created collection {collection_name}")
        return collection

    def _create_collection(self, collection_name: str = None) -> Collection | None:
        """创建或获取 Milvus 集合"""
        try:
            # 获取dim长度
            dim_length = 0
            if self._embeddings is not None:
                # 初始化进行一次向量化获取向量长度
                dim = self._embeddings.embed_query("获取长度")
                dim_length = len(dim)

            # 定义集合 schema
            fields = [
                FieldSchema(name=CollectionFields.id, dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name=CollectionFields.file_id, dtype=DataType.VARCHAR, max_length=256),
                FieldSchema(name=CollectionFields.file_trunk_id, dtype=DataType.VARCHAR, max_length=256),
                FieldSchema(name=CollectionFields.status, dtype=DataType.INT8),
                FieldSchema(name=CollectionFields.keywords, dtype=DataType.ARRAY, element_type=DataType.VARCHAR,
                            max_capacity=1000, max_length=256, nullable=True),
                FieldSchema(
                    name=CollectionFields.dense_vector,
                    dtype=DataType.FLOAT_VECTOR,
                    dim=dim_length,
                    enable_analyzer=True,
                    analyzer_params={"type": "chinese"},
                    enable_match=True
                ),
                FieldSchema(
                    name=CollectionFields.content,
                    dtype=DataType.VARCHAR,
                    max_length=65535,
                    enable_analyzer=True,
                    enable_match=True
                ),  # 存储原始文本
                FieldSchema(name=CollectionFields.metadata, dtype=DataType.VARCHAR, max_length=4096),
                # 稀疏向量字段（由函数自动生成）
                FieldSchema(name=CollectionFields.sparse_vector, dtype=DataType.SPARSE_FLOAT_VECTOR)
            ]
            schema = CollectionSchema(fields=fields, description="Vector database collection")

            # 定义 BM25 函数，将文本转为稀疏向量
            bm25_function = Function(
                name="text_bm25_emb",
                function_type=FunctionType.BM25,
                input_field_names=[CollectionFields.content],  # 输入字段为存储文本的字段
                output_field_names=[CollectionFields.sparse_vector]  # 输出字段为稀疏向量字段
            )
            schema.add_function(bm25_function)

            # 创建集合
            collection = Collection(name=collection_name, schema=schema, using=self.__db_name)
            self._create_indexes(collection)
            return collection
        except MilvusException as e:
            logger.error(f"Failed to create collection: {e}")
            return None

    def vector_base_init(self) -> bool:
        return self._init_milvus_connection(self.__config_dto)

    def _prepare_data_for_insert(self, info: KBFileTrunkInfo) -> dict:
        """准备文档数据以插入到集合中"""
        text = info.content
        return {
            CollectionFields.dense_vector: self._embeddings.embed_query(text),
            CollectionFields.content: text,
            CollectionFields.file_id: info.file_id,
            CollectionFields.status: 0,
            CollectionFields.metadata: str(info.meta_data),
            CollectionFields.keywords: info.keywords.split(","),
            CollectionFields.file_trunk_id: info.id
        }

    def _create_indexes(self, collection: Collection):
        """创建集合索引"""

        try:
            collection.create_index(
                field_name=CollectionFields.dense_vector,
                index_params=index_params.dense_vector
            )
            collection.create_index(
                field_name=CollectionFields.sparse_vector,
                index_params=index_params.sparse_vector
            )
        except MilvusException as e:
            logger.error(f"Error creating indexes: {e}")
            raise

    def save_trunks(self, trunks: List[KBFileTrunkInfo]) -> List[KBFileTrunkInfo]:
        data_list = [self._prepare_data_for_insert(info) for info in trunks]

        collection = self._get_collection(self.__collection_name)
        # 插入数据
        mr = collection.insert(data_list)

        for index, trunk in enumerate(trunks):
            if index < len(mr.primary_keys):
                trunk.stored_vector_id = mr.primary_keys[index]
            else:
                break

        collection.load()
        return trunks

    def _get_available_fields(self) -> list:
        """获取可查询的字段列表"""
        return [
            field for field in [
                CollectionFields.id,
                CollectionFields.file_id,
                CollectionFields.file_trunk_id,
                CollectionFields.status,
                CollectionFields.dense_vector,
                CollectionFields.content,
                CollectionFields.metadata
            ] if field != CollectionFields.sparse_vector
        ]

    def update_trunk(self, trunk: KBFileTrunkInfo) -> bool:
        """根据file_trunk_id更新向量集合中的指定向量"""
        try:
            collection = self._get_collection(self.__collection_name)
            # 查询指定 trunk
            results = collection.query(
                expr=f"{CollectionFields.file_trunk_id} == '{trunk.id}'",
                output_fields=self._get_available_fields()
            )
            if not results:
                logger.error(f"No data found for file_trunk_id: {trunk.id}")
                return False

            info = results[0]

            # 更新字段
            if trunk.content is not None:
                info[CollectionFields.content] = trunk.content
                info[CollectionFields.dense_vector] = self._embeddings.embed_query(trunk.content)
            if trunk.meta_data is not None:
                info[CollectionFields.metadata] = str(trunk.meta_data)
            if trunk.status is not None:
                info[CollectionFields.status] = trunk.status
            if trunk.keywords is not None:
                info[CollectionFields.keywords] = trunk.keywords

            # 执行更新操作
            collection.upsert(
                data=[info],
                primary_keys=[info[CollectionFields.id]]
            )
            collection.load()
            return True
        except MilvusException as e:
            logger.error(f"Error during update by file_trunk_id: {e}")
            return False

    def delete_trunk(self, trunk_id: str) -> bool:
        """通过 file_id 删除向量集合中的指定向量"""
        """通过 ID 删除文档分块"""
        try:
            expr = f"{CollectionFields.file_trunk_id} == '{trunk_id}'"
            self._get_collection().delete(expr)
            return True
        except Exception as e:
            logger.error(f"Error during delete by file ids: {e}")
        return False

    def _search(self, question: str, anns_field: str, file_ids: List[str] = None, limit: int = 5):
        """通用搜索方法"""
        query_vector = self._embeddings.embed_query(
            question) if anns_field == CollectionFields.dense_vector else question
        expr = self._query_build_expr(file_ids)

        try:
            collection = self._get_collection()
            # 设置搜索参数
            search_params = {
                "metric_type": SearchParams.metric_type,
                "params": {"nprobe": SearchParams.nprobe}
            }

            # 执行相似度搜索
            results = collection.search(
                data=[query_vector],
                anns_field=anns_field,
                param=search_params,
                expr=expr,
                limit=limit,
                output_fields=[
                    CollectionFields.id,
                    CollectionFields.content,
                    CollectionFields.metadata,
                    CollectionFields.file_trunk_id
                ]
            )
            return results
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    def _query_build_expr(self, file_ids: List[str] = None) -> Optional[str]:
        """构建 Milvus 查询表达式"""
        if file_ids is None or len(file_ids) == 0:
            return None
        # todo 考虑是否移除
        file_id_str_list = [str(file_id) for file_id in file_ids]
        return f"{CollectionFields.file_id} in {file_id_str_list} and {CollectionFields.status} == 0"

    def _filter_results(self, results) -> List[Document]:

        if not results[0]:
            return []

        """过滤搜索结果"""
        filtered_results = []
        for res in results[0]:
            # 确保访问实体字段的正确方式
            entity = res.entity
            if res.distance <= self.__score_threshold:
                filtered_results.append(transform_to_document(
                    id=entity.id,  # 使用 entity 访问字段
                    file_trunk_id=entity.file_trunk_id,
                    content=entity.content,
                    metadata=ast.literal_eval(entity.metadata),
                    distance=res.distance
                ))
        return filtered_results

    def similar_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        """根据查询向量进行相似度搜索"""
        results = self._search(query, CollectionFields.dense_vector, file_ids, self.__k)
        return self._filter_results(results)

    def hybrid_search(self, question: str, file_ids: List[str] = None) -> List[Document]:
        """执行混合搜索，同时使用向量搜索和 BM25"""
        try:
            collection = self._get_collection(self.__collection_name)
            expr = self._query_build_expr(file_ids)

            # 创建密集向量搜索请求
            request_dense = AnnSearchRequest(
                data=[self._embeddings.embed_query(question)],
                anns_field=CollectionFields.dense_vector,
                param={"metric_type": SearchParams.metric_type, "params": {"nprobe": SearchParams.nprobe}},
                limit=SearchParams.limit,
                expr=expr
            )

            # 创建 BM25 搜索标签请求数
            request_bm25 = AnnSearchRequest(
                data=[question],
                anns_field=CollectionFields.sparse_vector,
                param={"metric_type": "BM25"},
                limit=SearchParams.limit,
                expr=expr
            )

            # 合并请求
            reqs = [request_dense, request_bm25]
            # 执行混合检索
            results = collection.hybrid_search(
                reqs=reqs,
                rerank=WeightedRanker((1 - self.__bm25_weight), self.__bm25_weight),
                limit=SearchParams.limit,
                output_fields=[
                    CollectionFields.id,
                    CollectionFields.content,
                    CollectionFields.metadata,
                    CollectionFields.file_trunk_id
                ]
            )

            bm25_result = query_bm25_retriever(question, file_ids)
            mu_result = self._filter_results(results)

            return self.__merge_and_deduplicate(bm25_result, mu_result)
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []


    def __merge_and_deduplicate(self,bm25_list: List[Document], mil_list: List[Document]) -> List[Document]:
        """合并两个Document列表并根据page_content去重，保留第一个出现的文档"""
        # 将bm25List转换为Document对象
        seen = set()
        merged = []

        for doc in bm25_list + mil_list:
            content = doc.page_content
            if content not in seen:
                seen.add(content)
                merged.append(doc)
        return merged[:self.__k]

    def delete_by_file_id(self, file_id: str) -> bool:
        """通过 file_id 删除文档分块"""
        try:
            expr = f"{CollectionFields.file_id} == '{file_id}'"
            self._get_collection().delete(expr)
            return True
        except MilvusException as e:
            logger.error(f"Error during delete by file ids: {e}")
        return False
