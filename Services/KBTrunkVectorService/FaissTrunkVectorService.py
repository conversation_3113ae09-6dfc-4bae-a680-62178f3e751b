import logging
from typing import List

from langchain.retrievers import EnsembleRetriever
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever

from Configs.Config import SysConfig
from LLM.EmbeddingsManager import EmbeddingsManager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.KBTrunkVectorService.CustomBM25Retriever import get_bm25_retriever
from Services.KBTrunkVectorService.KBTrunkVectorBaseService import KBTrunkVectorBaseService
from Utils.FaissHelper import FaissHelper
from Utils.CommonUtils import transform_to_document


class FaissTrunkVectorService(KBTrunkVectorBaseService):

    def __init__(self, embeddings_helper: EmbeddingsManager):
        super().__init__(embeddings_helper)
        self.__faiss_helper = FaissHelper(embeddings_helper)
        self.__k: int = SysConfig["retrievers"]["vector"]["k"]
        self.__score_threshold: float = SysConfig["retrievers"]["vector"]["score_threshold"]
        self.__fetch_k: int = SysConfig["retrievers"]["vector"]["fetch_k"]
        self.__bm25_weight: float = SysConfig["retrievers"]["ensemble"]["bm25_weight"]

    def vector_base_init(self) -> bool:
        return True

    def save_trunks(self, trunks: List[KBFileTrunkInfo]) -> List[KBFileTrunkInfo]:
        docs = {}
        for trunk in trunks:
            doc = Document(page_content=trunk.content)
            doc.metadata = trunk.meta_data

            doc_list = docs.get(trunk.file_id, [])
            doc_list.append(doc)

            # 基于FAISS的特性，不会对存储的id更新，因此此处直接回写保存的id
            trunk.stored_vector_id = trunk.id

        for file_id, doc_list in docs:
            index_name = self.__faiss_helper.get_index_name_by_file_id(file_id)
            self.__faiss_helper.save_docs(doc_list, index_name, True)

        return trunks

    def similar_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        results = []

        for file_id in file_ids:
            try:
                index_name = self.__faiss_helper.get_index_name_by_file_id(file_id)
                vector_index = self.__faiss_helper.load(index_name=index_name)
                if vector_index is None:
                    continue

                index_retriever = vector_index.as_retriever(search_type="similarity_score_threshold",
                                                            search_kwargs={
                                                                "k": self.__k,
                                                                "score_threshold": self.__score_threshold,
                                                                "fetch_k": self.__fetch_k
                                                            })
                results.extend(index_retriever.invoke(query))
            except Exception as e:
                logging.error(f"FAISS向量检索错误：{e}")
        return [transform_to_document(id=result.id, file_trunk_id=result.id, content=result.page_content,
                                    metadata=result.metadata) for result in results]

    def _create_ensemble_retriever_by_file_id(self, file_id: str) -> BaseRetriever | None:
        bm25_retriever = get_bm25_retriever(file_ids=[file_id])
        file_index_name = self.__faiss_helper.get_index_name_by_file_id(file_id)
        vector_index = self.__faiss_helper.load(index_name=file_index_name)

        if not vector_index:
            return bm25_retriever

        index_retriever = vector_index.as_retriever(search_type="similarity_score_threshold",
                                                    search_kwargs={
                                                        "k": self.__k,
                                                        "score_threshold": self.__score_threshold,
                                                        "fetch_k": self.__fetch_k
                                                    })

        ensemble_retriever = EnsembleRetriever(
            retrievers=[bm25_retriever, index_retriever],
            weights=[self.__bm25_weight, (1 - self.__bm25_weight)])
        return ensemble_retriever

    def hybrid_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        results = []
        for file_id in file_ids:
            retriever = self._create_ensemble_retriever_by_file_id(file_id)
            if not retriever:
                results.extend(retriever.invoke(query))
        return [transform_to_document(id=result.id, file_trunk_id=result.id, content=result.page_content,
                                    metadata=result.metadata) for result in results]

    def delete_by_file_id(self, file_id: str) -> bool:
        file_index_name = self.__faiss_helper.get_index_name_by_file_id(file_id)
        return self.__faiss_helper.remove(file_index_name)
