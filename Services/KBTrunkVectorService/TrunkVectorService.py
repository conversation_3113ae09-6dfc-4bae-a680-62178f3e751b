from langchain_core.documents import Document
from Configs.Config import SysConfig
from LLM.LLMManager import embeddings_manager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo
from Services.KBTrunkVectorService.FaissTrunkVectorService import FaissTrunkVectorService
from Services.KBTrunkVectorService.KBTrunkVectorBaseService import KBTrunkVectorBaseService, KBTrunkVectorUpdateService
from Services.KBTrunkVectorService.LCMilvusTrunkVectorService import LCMilvusTrunkVectorService
from Services.KBTrunkVectorService.MilvusTrunkVectorService import MilvusTrunkVectorService
from Services.SqlServer.KBFileInfoService import KBFileInfoService


def __get_trunk_vector_service() -> KBTrunkVectorBaseService:
    trunk_service: KBTrunkVectorBaseService

    db_type = SysConfig["vector_db_type"]
    if db_type == 'milvus':
        trunk_service = LCMilvusTrunkVectorService(embeddings_manager)
        # trunk_service = MilvusTrunkVectorService(embeddings_manager)
    else:
        trunk_service = FaissTrunkVectorService(embeddings_manager)

    if trunk_service.vector_base_init():
        return trunk_service
    else:
        raise Exception("向量服务初始化失败")


# 文本块向量化服务的单例
kb_trunk_vector_service = __get_trunk_vector_service()


class TrunkVectorSaveService:
    @staticmethod
    def save_trunks(trunks: list[KBFileTrunkInfo]) -> list[KBFileTrunkInfo]:
        return kb_trunk_vector_service.save_trunks(trunks)

    @staticmethod
    def delete_by_file_id(file_id: str) -> bool:
        return kb_trunk_vector_service.delete_by_file_id(file_id)

    @staticmethod
    def update_trunk(trunk: KBFileTrunkInfo) -> bool:
        if isinstance(kb_trunk_vector_service, KBTrunkVectorUpdateService):
            return kb_trunk_vector_service.update_trunk(trunk)
        else:
            return False

    @staticmethod
    def delete_trunk(trunk_id: str) -> bool:
        if isinstance(kb_trunk_vector_service, KBTrunkVectorUpdateService):
            return kb_trunk_vector_service.delete_trunk(trunk_id)
        else:
            return False


class TrunkVectorRetrivalService:

    @staticmethod
    def vector_search(*, query: str, kb_ids: list[int] = None, file_ids: list[str] = None) -> list[Document]:
        file_ids = TrunkVectorRetrivalService.__filter_file_ids(kb_ids=kb_ids, file_ids=file_ids)
        if file_ids:
            return kb_trunk_vector_service.hybrid_search(query, file_ids)
        else:
            return []

    @staticmethod
    def hybrid_search(*, query: str, kb_ids: list[int] = None, file_ids: list[str] = None) -> list[Document]:
        file_ids = TrunkVectorRetrivalService.__filter_file_ids(kb_ids=kb_ids, file_ids=file_ids)
        if file_ids:
            return kb_trunk_vector_service.hybrid_search(query, file_ids)
        else:
            return []

    @staticmethod
    def __filter_file_ids(*, kb_ids: list[int] = None, file_ids: list[str] = None) -> list[str]:
        """根据存储的文件信息，过滤掉集合中无效的文件ID，只返回数据库中存在的有效文件ID

        Args:
            file_ids: 待过滤的文件ID列表，可能包含无效或不存在的ID

        Returns:
            经过过滤后的有效文件ID列表，确保所有返回的ID都存在于知识库中

        Note:
            这是一个私有方法，仅供类内部其他方法调用
        """
        file_service = KBFileInfoService()
        if kb_ids:
            files = file_service.select_by_kb_ids(kb_ids)
            if file_ids:
                kb_file_ids = set([file.file_id for file in files])
                kb_file_ids.update(file_ids)
                file_ids = kb_file_ids
            else:
                file_ids = [file.file_id for file in files]
        if file_ids:
            file_ids = file_service.select_valid_file_ids(file_ids)
        return file_ids
