from abc import ABC, abstractmethod
from typing import List

from langchain_core.documents import Document

from LLM.EmbeddingsManager import EmbeddingsManager
from Models.pojo.KBFileTrunkInfo import KBFileTrunkInfo


class KBTrunkVectorBaseService(ABC):

    def __init__(self, embeddings_helper: EmbeddingsManager):
        self._embeddings = embeddings_helper.get_embeddings()

    @abstractmethod
    def vector_base_init(self) -> bool:
        pass

    @abstractmethod
    def save_trunks(self, trunks: List[KBFileTrunkInfo]) -> List[KBFileTrunkInfo]:
        pass

    def similar_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        pass

    @abstractmethod
    def hybrid_search(self, query: str, file_ids: List[str] = None) -> List[Document]:
        pass

    @abstractmethod
    def delete_by_file_id(self, file_id: str) -> bool:
        pass


class KBTrunkVectorUpdateService(KBTrunkVectorBaseService):
    def __init__(self, embeddings_helper: EmbeddingsManager):
        super().__init__(embeddings_helper)

    @abstractmethod
    def update_trunk(self, trunk: KBFileTrunkInfo) -> bool:
        pass

    @abstractmethod
    def delete_trunk(self, trunk_id: str) -> bool:
        pass
