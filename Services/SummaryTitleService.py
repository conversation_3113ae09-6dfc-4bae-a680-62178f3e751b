from typing import List

from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, ChatPromptTemplate
from langchain_core.runnables import RunnableLambda

from LLM.LLMManager import sys_llm_manager
from Utils.CommonUtils import remove_think_tags


class SummaryTitleService:
    def __init__(self):
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        self._llmObj = llm_helper.get_llm_chat_object(temperature=0.1)

    def generate_title(self, messages: List, max_length: int = 10) -> str:
        # 构建提示模板

        system_template = SystemMessagePromptTemplate.from_template(
            """  你是一个专业标题生成专家，需要对问答内容进行总结和提炼从而生成一个简洁而精准的标题，长度不能超过 {word_limit} 字，以便快速传达核心信息。
                标题要求：
                1. 标题应简洁明了，避免冗长和复杂的表达，同时要确保信息的完整性和准确性。。
                2. 标题应为简短的文本形式，长度一般不超过 {word_limit} 个字。
                3. 深入理解问题内容，提取关键信息和核心要点。
                4. 根据提取的信息，用简洁明了的语言进行概括，形成初步标题。
                5. 对标题进行优化，确保其准确性、简洁性和吸引力。
                6. 如果无法从问答中提取相关信息，就根据问题性质生成一个简短的内容。
                示例：
                  - 例子1：问题内容：“如何通过合理饮食和运动改善身体健康？”
                    “健康改善：饮食与运动的结合”
                  - 例子2：问题内容：“在数字化时代，企业如何利用大数据提升营销效果？”
                    “大数据助力企业营销升级”
                  - 例子3：问题内容：“探讨人工智能在医疗领域的应用前景和挑战。”
                    “人工智能与医疗：机遇与挑战”
                /no_think""
           """
        )

        human_template = HumanMessagePromptTemplate.from_template(
            "对话内容：\n{history}"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ])

        # 创建 RunnableSequence
        chain = (prompt_template
                 | self._llmObj
                 | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                 | RunnableLambda(remove_think_tags))

        title_info = chain.invoke({
            "history": messages,
            "word_limit": max_length
        })
        return title_info