import threading
import time
from datetime import datetime

from Models.pojo.KBConversation import KBConversation
from Utils.logs.LoggingConfig import logger
from Configs.Config import SysConfig
from Models.pojo.KBConversationDetail import KBConversationDetail
from Utils.DBTools import MySQLBuilderEnhanced
from Services.SqlServer.KBConversationService import KBConversationService
from Utils.DBTools.SQLiteInitializer import SQLiteHandler

# 获取默认的数据库名称
user_db_name = SysConfig["sqlite"]["db_name"]


class ConversationMemory:
    def __init__(self):
        self.conversation_services = KBConversationService()
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()
        self.__operation_list: list[(str, KBConversationDetail)] = []  # FIFO队列
        self.__conn_map: dict[str, SQLiteHandler] = {}
        self.__work_thread: threading.Thread = threading.Thread(target=self.__work_loop)
        self.table_name = "kb_question_chat_logs"

        self.__is_working = False

    def __get_sqlite_handler(self, user_flag: str = user_db_name) -> SQLiteHandler:
        if user_flag not in self.__conn_map:
            self.__conn_map[user_flag] = SQLiteHandler(user_flag)
        return self.__conn_map[user_flag]

    def new_memory(self, user_flag: str = user_db_name, memory: KBConversationDetail = None):
        logger.info(f"user_flag:{user_flag}  ,new_memory:{memory}")
        self.__operation_list.append((user_flag, memory))

    def __work_loop(self):
        while self.__is_working:
            for operation in self.__operation_list:
                logger.info(f"__work_loop循环执行参数:{operation}")
                if operation is None:
                    continue
                user_flag = operation[0]
                memory = operation[1]
                logger.info(f"消息信息： user_flag:{user_flag} ,memory:{memory}")
                sqlite_handler = self.__get_sqlite_handler(user_flag)
                insert_sql, params = self.__db_builder.build_insert(memory, self.table_name, symbol="?")
                logger.info(f"插入消息记录的sql:{insert_sql},params:{params}")
                row = sqlite_handler.execute_non_query(insert_sql, params)
                logger.info(f"插入消息记录的结果:{row}")
                self.__operation_list.remove(operation)
                if row > 0:
                    data = KBConversation(id=memory.conversation_id,update_time=datetime.now())
                    self.conversation_services.update(data=data)
            time.sleep(1)

    def start(self):
        self.__is_working = True
        self.__work_thread.start()

    def stop(self):
        self.__is_working = False
        self.__work_thread.join()

    def query_conversation_details(self, user_flag: str = user_db_name, conversation_id=None, page_num=None,
                                   page_size=None):
        sqlite_handler = self.__get_sqlite_handler(user_flag)
        select_sql, params, total_sql, total_params = self.__db_builder.build_select_params(
            ["conversation_id"], [conversation_id],
            self.table_name,
            page_num, page_size,
            sort_field="message_time", sort="ASC",
            symbol="?")
        # 查询数据集
        total_count = 0

        # 查询总条数
        if total_sql is not None and total_params is not None:
            total = sqlite_handler.execute_query_one(total_sql, total_params)

            if total is not None:
                total_count = total.get("total", 0)
                # 查询数据集

        results = sqlite_handler.execute_query(select_sql, params)
        return [KBConversationDetail(**row) for row in results], total_count

    def delete_conversation_details(self, user_flag: str = user_db_name, data=None) -> int:
        sqlite_handler = self.__get_sqlite_handler(user_flag)
        delete_query = f"DELETE FROM {self.table_name} WHERE conversation_id = ?"
        return sqlite_handler.execute_non_query(delete_query, (data,))

    def edit_conversation_details(self, user_flag: str = user_db_name, data: KBConversationDetail = None) -> int:
        update_query, update_params = self.__db_builder.build_update(primary_key="message_id", obj=data,
                                                                     table_name=f"{self.table_name}")
        sqlite_handler = self.__get_sqlite_handler(user_flag)
        return sqlite_handler.execute_non_query(update_query, update_params)


conversation_memory_manager = ConversationMemory()
