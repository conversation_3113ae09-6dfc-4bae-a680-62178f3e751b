from Utils.logs.LoggingConfig import logger
from typing import  AsyncIterator
import json
from langchain.prompts import PromptTemplate
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory
from LLM.LLMManager import sys_llm_manager
from Configs.Config import SysConfig


def _validate_response(response: dict) -> bool:
    """验证响应结构"""
    return (
            isinstance(response, dict) and
            isinstance(response.get("variants"), list) and
            all(isinstance(v, str) for v in response["variants"])
    )


class QuestionExpander:
    def __init__(self):

        # 初始化 LLM
        temperature = SysConfig["agents"].get("customer_agent", {}).get("temperature", 0.7)
        self._llmObj = sys_llm_manager.get_summary_title_use_llm_helper().get_llm_object(temperature)

        self._memory = ChatMessageHistory()

        # 设置对话
        self.conversation = RunnableWithMessageHistory(
            runnable=self._llmObj,
            get_session_history=lambda: self._memory
        )

        self.prompt_template = PromptTemplate.from_template(
            """作为问题优化专家，请为以下问题生成{num_questions}个自然变体：
            
            原始问题：{question}
            
            生成要求：
            1. 保持核心意图不变
            2. 使用不同表达方式（专业/口语/场景化）
            3. 包含时间、范围等维度扩展
            4. 输出严格遵循以下JSON格式（不要包含任何其他内容）：
            {{
                "original": "原始问题文本",
                "variants": [
                    "变体问题1",
                    "变体问题2",
                    "变体问题3"
                ]
            }}"""
        )

    async def expand_question(self, question: str) -> AsyncIterator[str]:
        from typing import AsyncIterator

        # 输入验证
        if not isinstance(question, str) or len(question.strip()) == 0:
            logger.error("无效的问题输入")
            return

        response_content = ""
        try:
            # 从配置获取参数
            num_questions = SysConfig["agents"].get("customer_agent", {}).get("num_variants", 3)

            prompt = self.prompt_template.format(
                question=question,
                num_questions=num_questions  # 使用配置参数
            )

            try:
                async for chunk in self.conversation.astream({"input": prompt}):
                    content = chunk.content if hasattr(chunk, 'content') else str(chunk)
                    yield content
                    response_content += content

            except Exception as e:
                logger.error(f"流式响应异常: {str(e)}")
                raise

            # 数据解析与验证
            try:
                response = json.loads(response_content)
                if not _validate_response(response):
                    raise ValueError("响应格式验证失败")

                for variant in response["variants"]:
                    yield variant

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}，原始响应：{response_content}")

        except Exception as e:
            logger.error(f"处理流程异常: {str(e)}")
