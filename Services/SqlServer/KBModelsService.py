from typing import Optional

from pydantic import BaseModel

from Models.peewee.OrmModel import KBModel


class KBModelTBean(BaseModel):
    id: Optional[int] = None  # 主键
    platform_type: Optional[str] = None  # 平台类型  ModelsPlatformName 用这个类
    api_url: Optional[str] = None  # api地址
    api_secret: Optional[str] = None  # api密钥
    model_name: Optional[str] = None  # 模型名称
    model_think_type: Optional[int] = None  # 模型类型，0非推理，1推理，2可推理可非推理
    model_context: Optional[int] = None  # 模型上下文
    enable_state: Optional[int] = None  # 状态0不可用  1可用
    system_state: Optional[int] = None  # 状态0非系统  1系统
    use_state: Optional[int] = None  # 0默认聊天  1默认生成  2默认聊天又默认生成, 非默认就是none


class KBModelsService:

    @staticmethod
    def create(data: KBModelTBean):
        return KBModel(**data.model_dump(exclude_none=True)).cus_insert()

    @staticmethod
    def edit(data: KBModelTBean):
        # 0默认聊天  1默认生成  2默认聊天又默认生成
        if data.use_state == 0:
            chat_model = KBModelsService.get_default_chat_model()
            generate_model = KBModelsService.get_generate_model()

            if generate_model.id == data.id:
                if chat_model.id != generate_model.id:
                    chat_model.use_state = None
                    chat_model.save()
                data.use_state = 2
            elif chat_model.id == generate_model.id:
                generate_model.use_state = 1
                generate_model.save()
            else:
                chat_model.use_state = None
                chat_model.save()

        return KBModel.cus_dict_update_by_id(data.id, data.model_dump(exclude_none=True))

    @staticmethod
    def list(model_name: str = None):
        if model_name:
            rows = KBModel.select().where(KBModel.model_name.contains(model_name)).execute()
        else:
            rows = KBModel.select().execute()
        return [item.cus_model_to_dict() for item in rows]

    @staticmethod
    def list_available():
        rows = KBModel.select().where(KBModel.enable_state == 1).execute()
        return [item.cus_model_to_dict() for item in rows]

    @staticmethod
    def list_enable():
        rows = KBModel.select(KBModel.enable_state == 1).execute()
        return [item.cus_model_to_dict() for item in rows]

    @staticmethod
    def delete(id: int):
        return KBModel.delete_by_id(id)

    @staticmethod
    def get_chat_model(id: int):
        return KBModel.get_by_id(id)

    @staticmethod
    def get_default_chat_model():
        try:
            chat_model = KBModel.get(KBModel.use_state == 0)
        except:
            chat_model = KBModel.get(KBModel.use_state == 2)
        return chat_model

    @staticmethod
    def get_generate_model():
        try:
            generate_model = KBModel.get(KBModel.use_state == 1)
        except:
            generate_model = KBModel.get(KBModel.use_state == 2)
        return generate_model
