from Utils.logs.LoggingConfig import logger
from typing import List

from Configs.Config import SysConfig
from Models.pojo.KBConversationDetail import KBConversationDetail
from Utils.DBTools import SQLiteInitializer, MySQLBuilderEnhanced


class KBConversationDetailService:
    def __init__(self, db=None, question_flag=SysConfig["sqlite"]["db_name"]):
        self.__db = db or SQLiteInitializer.SQLiteHandler(question_flag)
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()
        self.table_name = "kb_question_chat_logs"

    # 查询数据库数据单条
    def select_by_question_id(self, id: str,page_num=None, page_size=None) -> List[KBConversationDetail]:
        """根据id查询数据库数据"""
        try:
            query = f"select * from {self.table_name} where question_id = ?"
            query_params = [id]  # 基础查询参数
            # 分页处理
            if page_size is not None and page_num is not None:
                offset = (page_num - 1) * page_size
                query += f" LIMIT ? OFFSET ?"  # 使用参数绑定
                query_params.extend([page_size, offset])  # 添加分页参数
            results = self.__db.execute_query(query, query_params)
            if results is None:
                return []
            return [KBConversationDetail(**row) for row in results]
        except Exception as e:
            logger.error(f"Error in select_by_id: {e}")
            raise

    # 插入数据库数据
    def insert(self, data: KBConversationDetail) -> int:
        """插入数据库数据"""
        try:
            insert_query, insert_params = self.__db_builder.build_insert(data, f"{self.table_name}",symbol="?")
            logger.info(f"insert_query: {insert_query}")
            return self.__db.execute_non_query(insert_query, insert_params)
        except Exception as e:
            logger.error(f"Error in insert: {e}")
            raise

    # 删除数据库数据
    def delete(self, id: int) -> int:
        """删除数据库数据"""
        try:
            delete_query = f"DELETE FROM {self.table_name} WHERE id = ?"
            return self.__db.execute_non_query(delete_query, id)
        except Exception as e:
            logger.error(f"Error in delete: {e}")
            raise

    def update(self, id: int, data: int) -> int:
        """更新数据库数据"""
        try:
            update_query = f"UPDATE {self.table_name} SET state = ? WHERE id = ?"
            update_params = (data, id)
            logger.info(f"update_query: {update_params}")
            row = self.__db.execute_non_query(update_query, update_params)
            logger.info(f"update row: {row}")
            return row
        except Exception as e:
            logger.error(f"Error in update: {e}")

    def delete_by_question_id(self, id):
        """删除数据库数据"""
        try:
            delete_query = f"DELETE FROM {self.table_name} WHERE question_id = ?"
            return self.__db.execute_non_query(delete_query, id)
        except Exception as e:
            logger.error(f"Error in delete: {e}")
            raise
