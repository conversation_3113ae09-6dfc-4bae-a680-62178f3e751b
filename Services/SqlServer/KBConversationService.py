from Utils.logs.LoggingConfig import logger
from datetime import datetime
from typing import List

from Models.pojo.KBConversation import KBConversation
from Utils.DBTools import MySQLInitializer, MySQLBuilderEnhanced


class KBConversationService:
    def __init__(self, db=None):
        self.__db = db or MySQLInitializer.MySQLHandler()
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()
        self.table_name = "kb_conversation"

    # 查询数据库数据单条
    def select_by_id(self, id: str) -> KBConversation:
        """根据id查询数据库数据"""
        try:
            query = f"select * from {self.table_name} where id = ?"
            ajax = self.__db.execute_query_one(query, [id])
            return KBConversation(**ajax)
        except Exception as e:
            logger.error(f"Error in select_by_id: {e}")
            raise

    # 查询数据库数据
    def select_by_group_id(self, group_id, page_num=None, page_size=None):
        """查询数据库数据"""
        try:
            select_sql, params, total_sql, total_params = self.__db_builder.build_select_params(
                ["group_id"], [group_id],
                self.table_name,
                page_num, page_size,
                sort_field="update_time", sort="DESC",
                symbol="%s")


            results = self.__db.execute_query(select_sql,params)
            total = self.__db.execute_query(total_sql,total_params)
            if results is None:
                return [],0
            return [KBConversation(**row) for row in results],total[0]["total"]
        except Exception as e:
            logger.error(f"Error in select_all: {e}")
            raise

    # 查询数据库数据
    def select_all(self, params: KBConversation = None, page_num=None, page_size=None) -> List[KBConversation]:
        """查询数据库数据"""
        try:
            query = f"select * from {self.table_name}"
            # 分页处理
            if page_size is not None and page_num is not None:
                offset = (page_num - 1) * page_size
                query += f" LIMIT {page_size} OFFSET {offset}"
            # 执行sql查询
            results = self.__db.execute_query(query, params)
            if results is None:
                return []
            return [KBConversation(**row) for row in results]
        except Exception as e:
            logger.error(f"Error in select_all: {e}")
            raise

    # 插入数据库数据
    def insert(self, data: KBConversation) -> int:
        """插入数据库数据"""
        try:
            data.create_time = datetime.now()
            data.update_time = datetime.now()
            insert_query, insert_params = self.__db_builder.build_insert(data, f"{self.table_name}")
            logger.info(f"insert_query: {insert_query}")
            return self.__db.execute_non_query(insert_query, insert_params)
        except Exception as e:
            logger.error(f"Error in insert: {e}")
            raise

    # 删除数据库数据
    def delete(self, id: str) -> int:
        """删除数据库数据"""
        try:
            delete_query = f"DELETE FROM {self.table_name} WHERE id = %s"
            return self.__db.execute_non_query(delete_query, id)
        except Exception as e:
            logger.error(f"Error in delete: {e}")
            raise

    def update(self, data: KBConversation) -> int:
        """更新数据库数据"""
        try:
            data.update_time = datetime.now()
            update_query, update_params = self.__db_builder.build_update(data, f"{self.table_name}")
            logger.info(f"update_query: {update_params}")
            row = self.__db.execute_non_query(update_query, update_params)
            logger.info(f"update row: {row}")
            return row
        except Exception as e:
            logger.error(f"Error in update: {e}")
