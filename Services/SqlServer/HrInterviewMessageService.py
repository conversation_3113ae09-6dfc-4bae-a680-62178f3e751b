from datetime import datetime
from typing import List
from Utils.DBTools import MySQLBuilderEnhanced, MySQLInitializer
from Models.pojo.HrInterviewMessageInfo import HrInterviewMessageInfo
from loguru import logger  # 新增日志记录


class HrInterviewMessageService:
    def __init__(self, db=None):
        self.__db = db or MySQLInitializer.MySQLHandler()
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()
        self.__table = "hr_interview_messages"  # 集中管理表名

    def __execute_query(self, query: str, params: tuple) -> List[dict]:
        """统一执行查询操作"""
        try:
            with self.__db.get_connection() as conn:  # 使用上下文管理器
                return conn.execute_query(query, params)
        except Exception as e:
            logger.error(f"数据库操作失败: {str(e)}")
            raise  # 向上抛出异常

    def select_all_messages(self, session_id: str) -> List[HrInterviewMessageInfo]:
        """获取指定会话的全部消息"""
        query = f"SELECT * FROM {self.__table} WHERE session_id = %s ORDER BY created_time ASC"
        results = self.__execute_query(query, (session_id,))
        return [HrInterviewMessageInfo(**row) for row in results]

    def insert_message(self, message: HrInterviewMessageInfo) -> int:
        """插入新消息记录"""
        insert_query, insert_params = self.__db_builder.build_insert(message, self.__table)
        logger.info(f"insert_params: {insert_params}")
        
        try:
            # 使用数据库连接池的默认事务管理
            self.__db.execute_non_query(insert_query, insert_params)
            
            # 获取最后插入ID
            result = self.__db.execute_query("SELECT LAST_INSERT_ID() as id")
            return result[0]['id']
            
        except Exception as e:
            logger.error(f"插入失败: {str(e)}")
            raise
        
    def update_message(self, message: HrInterviewMessageInfo):
        """更新消息记录"""
        message.update_time = datetime.now()
        update_query, update_params = self.__db_builder.build_update(message, self.__table)
        logger.info(f"update_query: {update_query}")
        logger.info(f"update_params: {update_params}")
        return self.__db.execute_non_query(update_query, update_params)

    def delete_message(self, session_id: str):
        """删除指定会话的所有消息"""
        delete_query, delete_params = self.__db_builder.build_delete(self.__table, {"session_id": session_id})
        logger.info(f"delete_query: {delete_query}")
        logger.info(f"delete_params: {delete_params}")
        return self.__db.execute_non_query(delete_query, delete_params)
