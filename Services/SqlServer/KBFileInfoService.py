import uuid
from datetime import datetime
from enum import Enum
from typing import List, Tuple

from Models.pojo.KBFileInfo import KBFileInfo
from Utils.DBTools import MySQLInitializer, MySQLBuilderEnhanced
from Utils.logs.LoggingConfig import logger


class KBFileInfoService:
    def __init__(self, db=None):
        self.__db = db or MySQLInitializer.MySQLHandler()
        self.__db_builder = MySQLBuilderEnhanced.MySQLBuilderEnhanced()


    # 查询数据库数据单条
    def select_by_id(self, id: str) -> KBFileInfo:
        """根据id查询数据库数据"""
        try:
            query = "select * from kb_file_info where file_id = %s"
            row = self.__db.execute_query_one(query, [id])
            if row is None:
                return None
            return KBFileInfo(**row)
        except Exception as e:
            logger.error(f"ID查询失败: {str(e)}", exc_info=True)  # 添加详细错误日志
            raise

    # 构建智能查询条件
    def build_query_conditions(self, params: KBFileInfo) -> tuple[str, list]:
        """构建智能查询条件"""
        where_clause = []
        params_list = []
        params_dict = params.dict(exclude_unset=True)

        for attr, value in params_dict.items():
            if attr in ['update_time', 'upload_time', "trunk_count"]:
                continue
            if value is not None:
                if attr == 'file_name' and '%' in value:
                    where_clause.append(f"{attr} LIKE %s")
                else:
                    where_clause.append(f"{attr} = %s")
                params_list.append(f"{value}")

        return " WHERE " + " AND ".join(where_clause) if where_clause else "", params_list

    def select_all(self, params: KBFileInfo, page_num=None, page_size=None) -> Tuple[List[KBFileInfo], int]:
        """查询数据库数据并返回结果列表和总数量"""
        try:
            # 原始查询
            if params.del_flag is None:
                params.del_flag = 0
            where_clause, params_list = self.build_query_conditions(params)
            data_query = "select * from kb_file_info" + where_clause

            # 修复分页参数计算
            data_query = data_query + " ORDER BY upload_time DESC"  # 确保ORDER BY在正确位置 

            if page_size and page_num:
                offset = (page_num - 1) * page_size
                data_query = f"{data_query} LIMIT {page_size} OFFSET {offset}"

            # 总数查询（移除分页）
            count_query = f"SELECT COUNT(*) FROM kb_file_info{where_clause}"

            # 执行查询
            results = self.__db.execute_query(data_query, params_list)
            total = self.__db.execute_query_one(count_query, params_list)

            if not results:
                return [], 0

            return [KBFileInfo(**row) for row in results if row], total['COUNT(*)']
        except Exception as e:
            logger.error(f"查询失败: {str(e)}")
            raise

    # 插入数据库数据
    def insert(self, data: KBFileInfo) -> dict:
        """插入数据库数据并返回完整文件信息"""
        try:
            # 自动生成必要字段
            if data.allow_download is None:
                data.allow_download = 1
            if not data.file_id:
                data.file_id = str(uuid.uuid4())
            if not data.upload_time:
                data.upload_time = datetime.now()
            data.update_time = datetime.now()
            data.file_original_name = data.file_name

            # 设置默认值
            if data.file_state is None:
                data.file_state = 0  # 默认未处理状态
            if data.file_enable is None:
                data.file_enable = 0  # 默认启用

            # 自动提取文件扩展名
            if data.file_name and '.' in data.file_name and data.file_extension is None:
                data.file_extension = data.file_name.split('.')[-1]

            insert_query, insert_params = self.__db_builder.build_insert(data, "kb_file_info")
            logger.info(f"insert_query: {insert_query}")
            logger.info(f"insert_params: {insert_params}")
            return self.__db.execute_non_query(insert_query, insert_params)
        except Exception as e:
            logger.error(f"插入失败: {str(e)}")
            raise

    # 删除数据库数据
    def delete(self, id: str) -> int:
        """删除数据库数据"""
        try:
            delete_query = "update kb_file_info WHERE file_id = %s"
            logger.info(f"delete_query: {delete_query}")
            logger.info(f"id: {id}")
            return self.__db.execute_non_query(delete_query, [id])
        except Exception as e:
            logger.error(f"Error in delete: {e}")
            raise

    def update(self, data: KBFileInfo) -> int:
        """更新数据库数据"""
        try:
            data.update_time = datetime.now()
            # 将枚举字段转换为基本类型
            if data.file_state is not None and isinstance(data.file_state, Enum):
                data.file_state = data.file_state.value

            update_query, update_params = self.__db_builder.build_update(
                data,
                "kb_file_info",
                primary_key="file_id",
                exclude=["upload_time"]
            )
            logger.info(f"update_query: {update_query}")
            logger.info(f"update_params: {update_params}")
            row = self.__db.execute_non_query(update_query, update_params)
            logger.info(f"update row: {row}")
            return row
        except Exception as e:
            logger.error(f"Error in update: {e}")
            raise

    def batch_delete(self, file_ids: str = None, kb_ids: str = None) -> int:
        """批量删除文件记录"""
        try:
            conditions = []
            params = ()
            where_clause = ""

            if file_ids is not None or kb_ids is not None:
                where_clause = " WHERE "
                if file_ids is not None:
                    where_clause += f"find_in_set (file_id,'{file_ids}')"
                if kb_ids is not None:
                    where_clause += f"find_in_set (kb_id,'{kb_ids}')"

            delete_query = f"update kb_file_info set del_flag = 1 {where_clause}"
            logger.info(f"批量删除SQL: {delete_query}")
            logger.info(f"批量删除参数: {params}")
            return self.__db.execute_non_query(delete_query, params)
        except Exception as e:
            logger.error(f"批量删除失败: {str(e)}")
            raise

    def batch_real_delete(self, file_ids: str = None, kb_ids: str = None) -> int:
        """批量删除文件记录"""
        try:
            params = ()
            where_clause = ""

            if file_ids is not None or kb_ids is not None:
                where_clause = " WHERE "
                if file_ids is not None:
                    where_clause += f"find_in_set (file_id,'{file_ids}')"
                if kb_ids is not None:
                    where_clause += f"find_in_set (kb_id,'{kb_ids}')"

            delete_query = f"delete from kb_file_info {where_clause}"
            logger.info(f"批量删除SQL: {delete_query}")
            logger.info(f"批量删除参数: {params}")
            return self.__db.execute_non_query(delete_query, params)
        except Exception as e:
            logger.error(f"批量删除失败: {str(e)}")
            raise

    def select_valid_file_ids(self, file_ids):
        """查询有效的文件ID"""
        try:
            query = f"SELECT file_id FROM kb_file_info WHERE file_state = 2 and file_enable = 0 and del_flag = 0"
            if isinstance(file_ids, list):
                file_ids_str = ','.join([f"'{id}'" for id in file_ids])
                query += f" AND file_id IN ({file_ids_str})"
            ids = self.__db.execute_query(query)
            logger.info(f"查询有效的文件ID SQL: {query}")
            logger.info(f"查询有效的文件ID 参数: {file_ids}")
            if ids is None:
                return []
            return [row['file_id'] for row in ids]
        except Exception as e:
            logger.error(f"查询有效的文件ID失败: {str(e)}")
            raise
        pass

    def select_by_file_ids(self, file_ids) -> List[KBFileInfo]:
        """根据ids查询文件"""
        try:
            query = f"SELECT * FROM kb_file_info where del_flag = 0 "
            if isinstance(file_ids, list):
                file_ids_str = ','.join([f"'{id}'" for id in file_ids])
                query += f" and file_id IN ({file_ids_str})"
            results = self.__db.execute_query(query)
            logger.info(f"根据ids查询文件 SQL: {query}")
            logger.info(f"根据ids查询文件 参数: {file_ids}")

            return [KBFileInfo(**row) for row in results if row]
        except Exception as e:
            logger.error(f"查询有效的文件ID失败: {str(e)}")
            raise
        pass

    def select_by_kb_ids(self, kb_ids:List[int]) -> List[KBFileInfo]|None:
        """根据ids查询文件"""
        try:
            query = f"SELECT * FROM kb_file_info where del_flag = 0 "
            if isinstance(kb_ids, list):
                file_ids_str = ','.join([f"'{id}'" for id in kb_ids])
                query += f" and kb_id IN ({file_ids_str})"
            results = self.__db.execute_query(query)
            logger.info(f"根据ids查询文件 SQL: {query}")
            logger.info(f"根据ids查询文件 参数: {kb_ids}")

            if results:
                return [KBFileInfo(**row) for row in results if row]
            else:
                return []
        except Exception as e:
            logger.error(f"查询有效的文件ID失败: {str(e)}")
        return []

    def select_by_kb_id(self, kb_id, file_state):
        """根据知识库Id查询文件"""
        try:
            query = f"SELECT * FROM kb_file_info where del_flag = 0 and kb_id = %s and file_state = %s"
            results = self.__db.execute_query(query, [kb_id, file_state])
            logger.info(f"根据知识库Id查询文件 SQL: {query}")
            logger.info(f"根据知识库Id查询文件 参数: {kb_id}")
            return [KBFileInfo(**row) for row in results if row]
        except Exception as e:
            logger.error(f"根据知识库Id查询文件: {str(e)}")
            raise
        pass

    def selectFileIdsByKbId(self, kb_id):
        """根据知识库ID查询文件ID列表"""
        try:
            query = f"SELECT file_id FROM kb_file_info WHERE del_flag = 0 and kb_id = %s"
            file_ids = self.__db.execute_query(query, [kb_id])
            if file_ids is None:
                return []
            return [row['file_id'] for row in file_ids]
        except Exception as e:
            logger.error(f"Error in selectFileIdsByKbId: {e}")
            raise
        pass
