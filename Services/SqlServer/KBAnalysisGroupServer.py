from Models.peewee.OrmModel import KBAnalysisGroup


class KBAnalysisGroupServer:

    @staticmethod
    def insert(data: KBAnalysisGroup):
        data.save()
        return data

    @staticmethod
    def query(*params,is_dic=False):
        return KBAnalysisGroup.cus_query_page(*params, page=1, page_size=10000, is_dic=is_dic)[0]

    @staticmethod
    def update_by_id(group_id: str, data: dict):
        return KBAnalysisGroup.cus_dict_update_by_id(group_id, data)

    @staticmethod
    def delete_by_id(group_id: int):
        return KBAnalysisGroup.delete_by_id(group_id)
