from langchain_core.language_models import BaseLLM, BaseChatModel
from langchain_openai import ChatOpenAI, OpenAI
from LLM.BaseLLMHelper import <PERSON><PERSON><PERSON><PERSON>elper
from Models.peewee.OrmModel import KBModel


class OpenAiLLMHelper(BaseLLMHelper):

    def __init__(self, model: KBModel = None):
        super().__init__(model)

    def get_llm_object(self, temperature: float,num_ctx:int =None) -> BaseLLM:
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        llm = OpenAI(base_url=self._model.api_url,
                     model=self._model.model_name,
                     api_key=self._model.api_secret,
                     temperature=temperature,
                     max_tokens=num_ctx)
        return llm

    def get_llm_chat_object(self, temperature: float,num_ctx:int =None) -> BaseChatModel:
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        llm = ChatOpenAI(base_url=self._model.api_url,
                         model=self._model.model_name,
                         api_key=self._model.api_secret,
                         temperature=temperature,
                         max_tokens=int(num_ctx))
        return llm

