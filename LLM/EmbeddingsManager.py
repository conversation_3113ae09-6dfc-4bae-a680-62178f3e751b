from langchain_core.embeddings import Embeddings
from langchain_ollama import OllamaEmbeddings

from Configs.Config import SysConfig
from LLM.Embeddings.LMStudioEmbeddings import LMStudioEmbeddings


class EmbeddingsManager:
    def __init__(self):
        self.__embeddings_name = SysConfig["embedding"]["name"]

        self.__embeddings = self.__load_embeddings(self.__embeddings_name)

    def __load_embeddings(self, embedding_name: str) -> Embeddings:
        section = SysConfig["embedding"][embedding_name]
        if embedding_name == "ollama":
            base_url = section["path"]
            model_name = section["model"]
            max_tokens = section["max_tokens"]
            return OllamaEmbeddings(base_url=base_url, model=model_name,num_ctx=int(max_tokens/3))
        elif embedding_name == "lmstudio":
            base_url = section["path"]
            model_name = section["model"]
            return LMStudioEmbeddings(base_url=base_url, model_name=model_name)

        raise '未能加载Embedding类型'

    def get_embeddings(self):
        return self.__embeddings

    def get_embeddings_name(self):
        return self.__embeddings_name
