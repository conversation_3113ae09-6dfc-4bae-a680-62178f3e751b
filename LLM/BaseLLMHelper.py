import enum
from abc import ABC, abstractmethod
from langchain_core.language_models import BaseLLM, BaseChatModel

from Models.peewee.OrmModel import KBModel


class LLMModelChatMode(enum.IntEnum):
    ALL = 2
    NO_THINK = 0
    THINK = 1


class BaseLLMHelper(ABC):

    def __init__(self,  model: KBModel = None):
        self._model = model

    @abstractmethod
    def get_llm_object(self, temperature: float,num_ctx:int =None) -> BaseLLM:
        pass

    @abstractmethod
    def get_llm_chat_object(self, temperature: float,num_ctx:int =None) -> BaseChatModel:
        pass

    def get_model_name(self) -> str:
        return self._model.model_name

    def get_model_id(self) -> int:
        return self._model.id

    def get_max_tokens(self) -> int:
        return self._model.model_context

    def get_think_mode_str(self, think_mode: LLMModelChatMode) -> str:
        # 如果只支持一种类型，则不需要根据think_mode_name来生成字符串
        if self._model.model_think_type == LLMModelChatMode.ALL:
            if think_mode == LLMModelChatMode.NO_THINK:
                return "/no_think"
            else:
                return "/think"
        return ""