from langchain_core.language_models import BaseLLM, BaseChatModel
from langchain_ollama import ChatOllama, OllamaLLM

from LLM.BaseLLMHelper import <PERSON><PERSON>MHelper
from Models.peewee.OrmModel import KBModel


class OllamaLLMHelper(BaseLLMHelper):

    def __init__(self, model: KBModel = None):
        super().__init__(model)

    def get_llm_object(self, temperature: float, num_ctx: int = None) -> BaseLLM:
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        llm = OllamaLLM(base_url=self._model.api_url,
                        model=self._model.model_name,
                        temperature=temperature,
                        num_ctx=num_ctx)
        return llm

    def get_llm_chat_object(self, temperature: float, num_ctx: int = None) -> BaseChatModel:
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        llm = ChatOllama(base_url=self._model.api_url,
                         model=self._model.model_name,
                         temperature=temperature,
                         num_ctx=num_ctx)
        return llm
