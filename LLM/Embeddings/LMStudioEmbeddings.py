from typing import List, Optional
from langchain_core.embeddings import Embeddings
import requests


class LMStudioEmbeddings(Embeddings):
    """自定义 LM Studio 本地 Embedding 模型封装"""

    def __init__(
            self,
            model_name: str = "bge-small",
            base_url: str = "http://localhost:1234/v1",
            timeout: int = 60,
    ):
        """
        初始化

        Args:
            model_name: LM Studio 中加载的模型名称
            base_url: LM Studio 的本地 API 地址 (默认 http://localhost:1234/v1)
            timeout: 请求超时时间（秒）
        """
        self.model_name = model_name
        self.base_url = base_url
        self.timeout = timeout

    def _post(self, text: str) -> List[float]:
        """发送请求到 LM Studio API"""
        try:
            response = requests.post(
                f"{self.base_url}/embeddings",
                json={"input": text, "model": self.model_name},
                headers={"Content-Type": "application/json"},
                timeout=self.timeout,
            )
            response.raise_for_status()  # 检查 HTTP 错误
            return response.json()["data"][0]["embedding"]
        except requests.exceptions.RequestException as e:
            raise ValueError(f"请求 LM Studio API 失败: {e}")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """批量生成文档 Embedding"""
        return [self._post(text) for text in texts]

    def embed_query(self, text: str) -> List[float]:
        """生成查询 Embedding"""
        return self._post(text)
