import csv
import os
from bs4 import BeautifulSoup
import chardet  # 新增编码检测库


def detect_file_encoding(file_path):
    """自动检测文件编码"""
    with open(file_path, 'rb') as f:
        rawdata = f.read(10000)  # 读取前10KB用于检测编码
    return chardet.detect(rawdata)['encoding']


def remove_tags_soup(text):
    """使用BeautifulSoup清除标签"""
    # 处理空值情况
    if not text:
        return ''
    soup = BeautifulSoup(text, "html.parser")
    return soup.get_text(separator=' ', strip=True)  # 添加空格分隔标签内容


def check_csv_format(file_path):
    try:
        # 生成输出文件路径
        base, ext = os.path.splitext(file_path)
        output_path = f"{base}_cleaned{ext}"

        # 自动检测源文件编码
        src_encoding = detect_file_encoding(file_path)
        print(f"检测到源文件编码: {src_encoding}")

        csv.field_size_limit(1000000)  # 设置为1MB

        with open(file_path, 'r', encoding=src_encoding) as infile, \
                open(output_path, 'w', encoding='utf-8', newline='') as outfile:  # 使用带BOM的UTF-8

            reader = csv.reader(infile)
            writer = csv.writer(outfile)

            # 写入UTF-8 BOM头（可选，根据需求启用）
            # outfile.write('\ufeff')

            # 读取并验证标题行
            try:
                headers = next(reader)

                print(headers is [])

                writer.writerow(["title", "content"])  # 写入新文件标题

                if len(headers) > 0:
                    print(f"CSV文件格式正确，共 {len(headers)} 个字段")
                    print("字段列表：", headers)
                else:
                    print("CSV文件格式错误：缺少标题行")
                    return
            except StopIteration:
                print("CSV文件为空")
                return

            # 处理数据行
            valid_rows = 0
            error_rows = 0

            for row_idx, row in enumerate(reader, start=2):
                try:
                    if len(row) == len(headers):
                        # 强制转换所有字段为UTF-8
                        processed_row = []
                        for i, cell in enumerate(row):
                            if i == 4:  # 第5或第6列
                                processed_row.append(remove_tags_soup(cell).strip())
                            elif i == 3:
                                processed_row.append(cell)

                        writer.writerow(processed_row)
                        valid_rows += 1

                    else:
                        print(f"行 {row_idx} 列数不匹配 | 预期: {len(headers)} | 实际: {len(row)}")
                        error_rows += 1
                except Exception as e:
                    print(f"行 {row_idx} 处理失败: {str(e)}")
                    error_rows += 1

            print(f"\n处理完成 | 有效行: {valid_rows} | 错误行: {error_rows}")
            print(f"已生成UTF-8编码文件：{output_path}")

    except FileNotFoundError:
        print("文件未找到，请确认文件路径是否正确")
    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")


# 使用示例
check_csv_format(r'C:\Users\<USER>\Desktop\测试文件\12345传统知识库全量.csv')
