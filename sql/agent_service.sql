/*
 Navicat Premium Data Transfer

 Source Server         : 109
 Source Server Type    : MySQL
 Source Server Version : 50739
 Source Host           : ************:3306
 Source Schema         : agent_service

 Target Server Type    : MySQL
 Target Server Version : 50739
 File Encoding         : 65001

 Date: 09/04/2025 13:49:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hzyp_agents
-- ----------------------------
DROP TABLE IF EXISTS `hzyp_agents`;
CREATE TABLE `hzyp_agents`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `file_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `content` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `state` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kb_conversation
-- ----------------------------
DROP TABLE IF EXISTS `kb_conversation`;
CREATE TABLE `kb_conversation`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `kb_id` bigint(20) NULL DEFAULT NULL,
  `file_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kb_file_info
-- ----------------------------
DROP TABLE IF EXISTS `kb_file_info`;
CREATE TABLE `kb_file_info`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content_type` int(11) NOT NULL COMMENT '0: file, 1: text, 2: html',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `kb_id` bigint(20) NOT NULL,
  `gen_state` int(11) NOT NULL COMMENT '未生成: 0, 已生成: 1, 生成中: 2, 生成失败: 3',
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kb_file_trunk_info
-- ----------------------------
DROP TABLE IF EXISTS `kb_file_trunk_info`;
CREATE TABLE `kb_file_trunk_info`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `kb_id` bigint(20) NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `state` int(11) NOT NULL COMMENT '正常: 0, 禁用: 1, 失效: 2 (用户修改后，待重新生成)',
  `meta_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件块信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for kb_info
-- ----------------------------
DROP TABLE IF EXISTS `kb_info`;
CREATE TABLE `kb_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `kb_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识库名称',
  `file_count` int(11) NULL DEFAULT 0 COMMENT '文件数量',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `order_num` int(11) NULL DEFAULT NULL COMMENT '排序字段',
  `created_by` bigint(20) NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库信息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
