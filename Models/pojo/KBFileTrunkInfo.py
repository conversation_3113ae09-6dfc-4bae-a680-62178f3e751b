from datetime import datetime

from pydantic import BaseModel
from typing import Any


class KBFileTrunkInfo(BaseModel):
    id: str | None = None
    file_id: str | None = None
    kb_id: int | None = None
    status: int | None = None
    sort: int | None = None
    content: str | None = None
    meta_data: dict | None = None
    keywords: str | None = None
    create_time: datetime | None = None
    update_time: datetime | None = None
    del_flag: int | None = None
    # 向量保存之后的ID
    stored_vector_id: str | None = None

    def __init__(self, /, id=None, file_id=None, sort=None, content=None, kb_id=None, status=None, meta_data=None,
                 keywords=None, create_time=None, del_flag=None,
                 update_time=None,
                 **data: Any):
        super().__init__(**data)
        self.id = id
        self.file_id = file_id
        self.sort = sort
        self.content = content
        self.kb_id = kb_id
        self.status = status
        self.meta_data = meta_data
        self.keywords = keywords
        self.create_time = create_time
        self.update_time = update_time
        self.del_flag = del_flag

    def to_dict(self):

        data = {
            "id": self.id,
            "fileId": self.file_id,
            "sort": self.sort,
            "content": self.content,
            "kbId": self.kb_id,
            "status": self.status,
            "metaData": self.meta_data,
            "keyWords": self.keywords,
            "delFlag": self.del_flag
        }
        if self.create_time:
            data["createTime"] = self.create_time.strftime("%Y-%m-%d %H:%M:%S")
        if self.update_time:
            data["updateTime"] = self.update_time.strftime("%Y-%m-%d %H:%M:%S")
        return data

    def __iter__(self):
        yield self.id
        yield self.file_id
        yield self.sort
        yield self.content
        yield self.kb_id
        yield self.status
        yield self.meta_data
        yield self.keywords
        yield self.create_time
        yield self.update_time
        yield self.del_flag

    class Config:
        from_attributes = True
        json_schema_extra = {
            "comment": "知识库文件内容表"
        }
