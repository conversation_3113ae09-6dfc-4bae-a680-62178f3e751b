import json
from pydantic import BaseModel
from datetime import datetime
from typing import Any

# 面试对话消息信息
class HrInterviewMessageInfo(BaseModel):
    id: int | None = None
    session_id: str | None = None
    create_time: datetime | None = None
    update_time: datetime | None = None
    content: str | None = None
    speaker_id: int | None = None
    speaker_name: str | None = None
    speaker_avatar: str | None = None
    speaker_role: str | None = None
    
    def __init__(self, **data: Any):
        super().__init__(**data)
        self.create_time = data.get('create_time', datetime.now())
        self.update_time = data.get('update_time', datetime.now())
        self.speaker_id = data.get('speaker_id', 0)
        self.speaker_name = data.get('speaker_name', '')
        self.speaker_avatar = data.get('speaker_avatar', '')
        self.speaker_role = data.get('speaker_role', '')
        self.content = data.get('content', '')
    
    def to_dict(self):
        return {
            "id": self.id,
            "create_time": self.create_time,
            "update_time": self.update_time,
            "content": self.content,
            "speaker_id": self.speaker_id,
            "speaker_name": self.speaker_name,
            "speaker_avatar": self.speaker_avatar,
            "speaker_role": self.speaker_role,
        }

    def __iter__(self):
        return iter(self.to_dict())
    class Config:
        orm_mode = True
        extra = 'ignore'
        json_schema_extra = {
            "comment": "面试对话消息信息表"
        }