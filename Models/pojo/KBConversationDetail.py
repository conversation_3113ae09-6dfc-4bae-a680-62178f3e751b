from datetime import datetime

from pydantic import BaseModel


class KBConversationDetail(BaseModel):
    """
    知识库问题聊天记录
    """
    message_id: int | None = None
    conversation_id: str | None = None
    content: str | None = None
    relatived_file: str | None = None
    message_type: str | None = None
    message_time: datetime = None

    class Config:
        from_attributes = True
        json_schema_extra = {
            "comment": "知识库问题聊天记录"
        }

    def __iter__(self):
        yield self.message_id
        yield self.conversation_id
        yield self.content
        yield self.relatived_file
        yield self.message_type
        yield self.message_time
