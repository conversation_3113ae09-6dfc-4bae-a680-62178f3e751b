from datetime import datetime
from typing import Any

from pydantic import BaseModel


class KBConversation(BaseModel):
    """
    知识库问题表
    """
    id: str | None = None
    group_id: str | None = None
    kb_id: int | None = None
    title: str | None = None
    create_time: datetime = None
    update_time: datetime = None

    class Config:
        from_attributes = True

    def __iter__(self):
        yield self.id
        yield self.group_id
        yield self.kb_id
        yield self.title
        yield self.create_time
        yield self.update_time

    def __init__(self, /, id=None, group_id = None, kb_id = None, title=None, create_time=None,
                 update_time=None, **data: Any):
        super().__init__(**data)
        self.id = id
        self.group_id = group_id
        self.kb_id = kb_id
        self.title = title
        self.create_time = create_time
        self.update_time = update_time
