from dataclasses_json import dataclass_json, LetterCase
from dataclasses import dataclass


@dataclass_json(letter_case=LetterCase.CAMEL)
@dataclass
class TaskInfoDto:
    educationWeight: int
    workExperienceWeight: int
    jobHoppingRateWeight: int
    salaryRangeWeight: int
    passScore: float | None
    minimumEducation: int | None
    experienceLowerBound: int | None
    experienceUpperBound: int | None
    jobHoppingYears: int | None
    jobHoppingCount: int | None
    salaryRangeLowerBound: int | None
    salaryRangeUpperBound: int | None
    educationMap: dict | None
    screeningConditions: str | None