from enum import Enum, StrEnum

from peewee import AutoField, CharField, IntegerField, SmallIntegerField, UUIDField, TextField

from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger

from Models.peewee.BaseModel import BaseModel, mysql_db, MediumTextField


class TaskType(Enum):
    LAY = 1


class TaskState(Enum):
    # 推荐使用10为间隔的数值（如10,20,30...），便于后续插入中间状态：
    WAITING = 10
    DOWNLOADING = 20
    DOWNLOADED = 30
    PROCESSING = 40
    COMPLETED = 50
    FAILED = 60
    CANCELED = 70


class CommonEnableType(Enum):
    LAY = 1


# 任务组
class KBAnalysisGroup(BaseModel):
    id = AutoField(primary_key=True)  # 主键
    user_id = CharField(max_length=40)  # 用户名
    title = CharField(max_length=60, null=True)  # 标题
    description = CharField(max_length=255, null=True)  # 描述
    _state = SmallIntegerField(default=TaskState.WAITING.value, column_name="state")  # 状态

    @classmethod
    def state_equals(cls, state_enum: TaskState):
        """生成状态等于的条件表达式"""
        return cls._state == state_enum.value

    @property
    def state(self) -> TaskState:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskState(self._state)
        except ValueError:
            return TaskState.WAITING  # 默认值处理异常情况

    @state.setter
    def state(self, value: TaskState):
        """将枚举对象存入数据库字段"""
        if not isinstance(value, TaskState):
            raise ValueError("必须传入TaskType枚举类型")
        self._state = value.value


# 任务
class KBAnalysisTask(BaseModel):
    id = AutoField(primary_key=True)  # 主键
    group_id = IntegerField()  # 工作组ID
    file_name = CharField(null=True)  # 原始文件名
    file_url = CharField(null=True)  # 文件地址
    file_size = IntegerField(null=True, default=0)  # 文件大小
    chunk_count = IntegerField(null=True, default=0)
    deal_chunk_count = IntegerField(null=True, default=0)
    result = MediumTextField(null=True)
    _task_type = SmallIntegerField(column_name="task_type", default=TaskType.LAY.value)  # 类型
    _state = SmallIntegerField(column_name="state", default=TaskState.WAITING.value)  # 状态

    @classmethod
    def state_equals(cls, state_enum: TaskState):
        """生成状态等于的条件表达式"""
        return cls._state == state_enum.value

    @property
    def state(self) -> TaskState:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskState(self._state)
        except ValueError:
            return TaskState.WAITING  # 默认值处理异常情况

    @state.setter
    def state(self, value: TaskState):
        self._state = value.value

    @property
    def task_type(self) -> TaskType:
        """将数据库整型值转为枚举对象"""
        try:
            return TaskType(self._task_type)
        except ValueError:
            return TaskType.LAY  # 默认值处理异常情况

    @task_type.setter
    def task_type(self, value: TaskType):
        self._task_type = value.value


class ModelsPlatformName(StrEnum):
    OLLAMA = "ollama"
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    SPARK = "spark"
    QWEN = "qwen"


# 模型列表
class KBModel(BaseModel):
    id = AutoField(primary_key=True)  # 主键
    platform_type = CharField(null=True)  # 平台类型  ModelsPlatformName 用这个类
    api_url = CharField(null=True)  # api地址
    api_secret = CharField(null=True)  # api密钥
    model_name = CharField(null=True)  # 模型名称
    model_think_type = IntegerField(null=True)  # 模型类型，0非推理，1推理，2可推理可非推理
    model_context = IntegerField(null=True,default=4096)  # 模型上下文
    enable_state = SmallIntegerField(default=1)  # 状态 0不可用  1可用
    system_state = SmallIntegerField(default=0)  # 状态 0非系统  1系统
    use_state = SmallIntegerField(null=True)  # 0默认聊天  1默认生成  2默认聊天又默认生成


def init_model_data():
    # 将系统默认模型插入
    models_count = KBModel.select().count()
    if models_count == 0:
        # 生成默认数据
        chat_use = SysConfig['llm']['chat-use']
        generate_use = SysConfig['llm']['generate-use']
        llm_list = SysConfig['llm']['llm_list']
        for llm in llm_list:
            if llm['model_think'] == 'no_think':
                model_think_type = 0
            elif llm['model_think'] == 'think':
                model_think_type = 1
            else:
                model_think_type = 2

            if llm['model'] == chat_use and llm['model'] == generate_use:
                use_state = 2
            elif llm['model'] == generate_use:
                use_state = 1
            elif llm['model'] == chat_use:
                use_state = 0
            else:
                use_state =None

            KBModel.create(platform_type=llm['engine'],
                           api_url=llm['base_url'],
                           api_secret="",
                           model_name=llm['model'],
                           model_think_type=model_think_type,
                           enable_state=1,
                           system_state=1,
                           use_state=use_state)

def create_tables():  # 在文件底部添加连接测试
    try:
        mysql_db.connect(reuse_if_open=True)
        mysql_db.create_tables([KBAnalysisGroup, KBAnalysisTask, KBModel])
        init_model_data()
        logger.info("表创建成功")  # 需要导入logger
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise
    finally:
        if not mysql_db.is_closed():
            mysql_db.close()
