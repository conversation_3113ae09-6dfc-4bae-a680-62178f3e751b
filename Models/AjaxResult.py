from fastapi import HTTPException
from typing import Dict, Any, Optional, Union
from pydantic import BaseModel


class AjaxResult(BaseModel):
    code: int
    message: str
    data: Optional[Union[str, Any]] = None  # 使用Optional表示可选字段

    @classmethod
    def success(cls, data: Optional[Union[Dict[str, Any], Any]] = None):
        """成功响应"""
        if data is None:
            return {"code": 200, "message": "success"}
        return {"code": 200, "message": "success", "data": data}

    @classmethod
    def success_for_rows(cls, total: int = 0, rows: Optional[Union[Dict[str, Any], Any]] = None) -> Dict:
        """分页成功响应"""
        if rows is None:
            rows = []
        return {
            "code": 200,
            "message": "success",
            "data": {
                "total": total,
                "rows": rows,
            }
        }

    @classmethod
    def error(cls, message: str = "error", data: Optional[Dict[str, Any]] = None) -> 'AjaxResult':
        """错误响应"""
        return cls(code=500, message=message, data=data)

    @classmethod
    def customize_success(cls, code: int = 200, message: str = "success",
                          data: Optional[Union[Dict[str, Any], Any]] = None) -> 'AjaxResult':
        """成功响应"""
        if data is None:
            return cls(code=code, message=message)
        if not isinstance(data, dict):
            data = {"result": data}
        return cls(code=code, message=message, data=data)

    @classmethod
    def customize_error(cls, code: int = 500, message: str = "error",
                        data: Optional[Dict[str, Any]] = None) -> 'AjaxResult':
        """错误响应"""
        return cls(code=code, message=message, data=data)

    @staticmethod
    def handle_exception(exc: Exception) -> HTTPException:
        """处理异常并返回统一的错误响应"""
        return HTTPException(
            status_code=500,
            detail={
                "code": 500,
                "message": f"Error: {str(exc)}",
                "data": None
            }
        )


