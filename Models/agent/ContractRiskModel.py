from pydantic import BaseModel, Field


class ContractRisk(BaseModel):
    clause: str = Field(description="合同条款的具体位置，保存完成的一句话。")
    risk_description: str = Field(description="具体描述风险性质（如条款模糊、责任不对等）及可能导致的后果")
    suggestion: str = Field(description="针对性的条款修改方案，包括删除、重写或补充的具体操作建议")
    risk_level: str = Field(default="低",description="风险等级评定（高/中/低），根据风险发生概率和影响程度综合评定。示例：高")
    legal_basis: str = Field(default="",description="援引的具体法律名称，条款号及内容概述。示例：《民法典》第一百九十三条,人民法院不得主动适用诉讼时效的规定。")


class ContractRiskList(BaseModel):
    datas: list[ContractRisk]  # 核心数组定义


class ContractRiskResult(BaseModel):
    high_risk: list[ContractRisk] = []  # 核心数组定义
    medium_risk: list[ContractRisk] = []  # 核心数组定义
    low_risk: list[ContractRisk] = []  # 核心数组定义
    high_risk_count: int = 0
    medium_risk_count: int = 0
    low_risk_count: int = 0
    risk_total_count: int = 0
