from typing import Optional

from fastapi import APIRouter
from pydantic import BaseModel

from LLM.LLMManager import sys_llm_manager
from Models.AjaxResult import AjaxResult
from Models.peewee.OrmModel import ModelsPlatformName
from Services.SqlServer.KBModelsService import KBModelsService, KBModelTBean

router = APIRouter(prefix="/agentService/api/llmSetting", tags=["llmSetting"])


class LLMModelSetting(BaseModel):
    name: str
    model_think: str


@router.get("/list")
def llm_list(model_name: Optional[str] = None):
    rows = KBModelsService.list(model_name)
    return AjaxResult.success(rows)

@router.get("/availableList")
def llm_available_list():
    rows = KBModelsService.list_available()
    return AjaxResult.success(rows)


@router.get("/get")
def llm_get(id: int):
    mode = KBModelsService.get_chat_model(id)
    return AjaxResult.success(mode.cus_model_to_dict())


@router.get("/run")
def llm_run(id=int):
    try:
        llm = sys_llm_manager.get_llm_helper(id=id).get_llm_chat_object(0.0)
        messages = [{"role": "user", "content": "Hi"}]
        llm.invoke(messages)
    except Exception as e:
        return AjaxResult.error("模型链接错误，请检查配置")
    return AjaxResult.success(1)


@router.get("/platformList")
def llm_platform_list():
    platform_list = [member.value for member in ModelsPlatformName]
    return AjaxResult.success(platform_list)


@router.post("/edit")
def llm_edit(request: KBModelTBean):
    KBModelsService.edit(request)
    return AjaxResult.success(1)

@router.post("/create")
def llm_create(request: KBModelTBean):
    try:
        KBModelsService.create(request)
    except:
        return AjaxResult.error("创建失败")
    return AjaxResult.success(1)



class RequestModelDeleteBean(BaseModel):
    id: int  # 主键


@router.post("/delete")
def llm_edit(request: RequestModelDeleteBean):
    KBModelsService.delete(request.id)
    return AjaxResult.success(1)
