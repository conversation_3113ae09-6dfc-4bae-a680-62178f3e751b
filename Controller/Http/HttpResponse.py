from pydantic import BaseModel


class HttpResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: dict = None

    @staticmethod
    def Success(message: str = "success", data: dict = None):
        return {
            "code": 200,
            "message": message,
            "data": data if data is not None else {}
        }

    @staticmethod
    def Error(code: int = 500, message: str = "error", data: dict = None):
        return {
            "code": code,
            "message": message,
            "data": data if data is not None else {}
        }
