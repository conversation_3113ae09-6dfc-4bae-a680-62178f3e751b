from fastapi import APIRouter
from openai import BaseModel

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Agents.InterviewQuestionsAgent import InterviewQuestionsAgent
from Models.AjaxResult import AjaxResult

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/interview", tags=["talent"])


class QuestionsInfo(BaseModel):
    # 招聘岗位
    position: str = ""
    question: str = ""
    jobDesc: str = ""
    userInfo: str = ""

# 面试内容
class interviewContent(BaseModel):
    # 内容
    content: str = ""
    question: str = ""
    interviewRounds: str = ""



@router.post("/list")
async def chat(info: QuestionsInfo):
    try:
        if not info:
            raise AjaxResult.error()

        agent = InterviewQuestionsAgent()
        body = await agent._formatting(info.position,info.jobDesc,info.userInfo)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


@router.post("/info")
async def chat(info: QuestionsInfo):
    try:
        if not info:
            raise AjaxResult.error()

        agent = InterviewQuestionsAgent()
        body = await agent._formatting_info(info.position, info.question,info.jobDesc,info.userInfo)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


@router.post("/improveInfo")
async def improveInfo(info: interviewContent):
    try:
        if info is None:
            raise AjaxResult.error("参数不能为空")
        agent = ImproveInfoAgent()
        body = await agent.hireImproveInfo(info.position, info.content, info.interviewRounds)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message="完善信息失败")
