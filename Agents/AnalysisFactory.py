from Agents.BaseAnalysisAgent import BaseAnalysisAgent, AnalysisSqlRequest
from Agents.ContractReviewAgent import ContractReviewAgent
from Models.peewee.OrmModel import TaskType, KBAnalysisTask
from Utils.CommonUtils import from_json_str
from Utils.logs.LoggingConfig import logger


class AnalysisFactory:

    @staticmethod
    def get_analysis_agent(task_type: TaskType) -> BaseAnalysisAgent | None:
        try:
            if task_type == TaskType.LAY:
                return ContractReviewAgent()
            return None
        except Exception as e:
            logger.error(f"创建出错: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def get_analysis_result_by_bean(task: KBAnalysisTask) -> dict | None:
        try:
            agent = AnalysisFactory.get_analysis_agent(task.task_type)
            t_type = agent.get_data_model()
            if not task.result:
                return None
            res = from_json_str(task.result, AnalysisSqlRequest[t_type])
            task_result = agent.deal_result(res.contract_content)
            return task_result.model_dump()
        except Exception as e:
            logger.error(f"创建出错: {str(e)}", exc_info=True)
            return None
