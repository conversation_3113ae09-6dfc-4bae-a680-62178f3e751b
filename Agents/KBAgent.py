from typing import List, Any

from langchain_core.documents import Document
from langchain_core.messages import trim_messages
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory

from Agents.ChatAgent import ChatAgent, ChatPreTaskResult
from Configs.Config import SysConfig
from LLM.BaseLLMHelper import BaseLLMHelper
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorRetrivalService
from Utils.BaiduSearchUtil import BaiduSearchUtil


class KBAgent(ChatAgent):
    file_ids: List[str] = []

    def __init__(self, conversation_id: str, group_id: str = None):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        super().__init__(conversation_id, group_id)

    def _create_conversation_obj(self, llm_helper: BaseLLMHelper, think_mode_str: str) -> RunnableWithMessageHistory:
        llm_obj = llm_helper.get_llm_chat_object(self.__temperature,10000)

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """你是一个专业的技术顾问，回答必须基于知识库原文，避免推测或外部知识补充。
                    回答指南:
                        1、优先引用：使用知识库原文的完整信息片段。
                        2、自然表述：在准确匹配基础上进行口语化转述。
                        3、明确说明：无匹配信息时清晰告知知识库未覆盖。
                        4、信息整合：必要时允许自然衔接不同段落信息.
                        5、专业态度：保持技术文档的中立性与完整性。
                    注意事项:
                        1. 用户提问中包含专业术语时，应使用原文术语。
                        2. 用户如果使用"Hi"、"哈喽"等问候语时，要友好的询问用户需要什么帮助。
                    {think}"""
                ),
                MessagesPlaceholder(variable_name="history"),
                ("human", "{input}"),
            ]
        ).partial(think=think_mode_str)

        trimmer = trim_messages(
            max_tokens=10000,
            include_system=True,
            strategy="last",
            token_counter=ChatAgent.tiktoken_counter,
            allow_partial=True
        )

        runnable = prompt | trimmer | llm_obj

        conversation = RunnableWithMessageHistory(
            runnable=runnable,
            get_session_history=lambda: self._memory,  # 返回整个 self._memory 对象
            input_messages_key="input",
            history_messages_key="history",
        )
        return conversation

    def load_kb_index(self, current_file_ids: List[str]):
        # 数据文件相同不重复创建检索器
        if current_file_ids == self.file_ids:
            return
        self.file_ids = current_file_ids

    def query_kb_knowledge(self, question) -> List[Document]:
        return TrunkVectorRetrivalService.hybrid_search(query=question, file_ids=self.file_ids)

    def query_internet_search(self, question) -> str:
        if self.internet_search:
            res = BaiduSearchUtil().search(query=question)
            return "\n".join([doc['content'] for doc in res])
        return ""

    def _pre_chat(self, user_input: str) -> ChatPreTaskResult:
        query_results = self.query_kb_knowledge(user_input)
        knowledge_result = ""
        if self.internet_search:
            knowledge_result = self.query_internet_search(user_input)
        else:
            if not query_results:
                return ChatPreTaskResult(result=False, message="抱歉，知识库中没有找到相关的信息。")

        if query_results:
            knowledge_result = knowledge_result + "\n".join([doc.page_content for doc in query_results])
        return ChatPreTaskResult(result=True, args=knowledge_result)


    def _generate_conversation_input(self, input: str, args: Any) -> {}:
        try:
            if not isinstance(args, str):
                return None
            knowledge_result = str(args)

            return {"input": f"<问题> {input} </问题>\n<知识库内容> {knowledge_result} </知识库内容>"}

        except Exception as e:
            print(f"Error in chat_for_answer: {str(e)}")
            return None
