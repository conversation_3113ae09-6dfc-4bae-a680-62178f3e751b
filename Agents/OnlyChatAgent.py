from typing import Any

from langchain_core.messages import trim_messages
from langchain_core.prompts import ChatPromptTemplate, \
    MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory

from Agents.ChatAgent import ChatAgent, ChatPreTaskResult
from Configs.Config import SysConfig
from LLM.BaseLLMHelper import BaseLLMHelper
from Utils.BaiduSearchUtil import BaiduSearchUtil


class OnlyChatAgent(ChatAgent):

    def __init__(self, conversation_id: str, group_id: str = None):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        super().__init__(conversation_id, group_id)

    def _create_conversation_obj(self, llm_helper: BaseLLMHelper, think_mode_str: str) -> RunnableWithMessageHistory:

        llm_obj = llm_helper.get_llm_chat_object(self.__temperature,10000)

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """你是一个专业的问答助手，请用通俗易懂的语言回答用户问题。
                    {think}"""
                ),
                MessagesPlaceholder(variable_name="history"),
                ("human", "{input}"),
            ]
        ).partial(think=think_mode_str)

        trimmer = trim_messages(
            max_tokens=10000,
            include_system=True,
            strategy="last",
            token_counter=ChatAgent.tiktoken_counter,
            allow_partial=True
        )

        runnable = prompt | trimmer | llm_obj

        conversation = RunnableWithMessageHistory(
            runnable=runnable,
            get_session_history=lambda: self._memory,  # 返回整个 self._memory 对象
            input_messages_key="input",
            history_messages_key="history",
        )
        return conversation

    def query_internet_search(self, question) -> str:
        if self.internet_search:
            res = BaiduSearchUtil().search(query=question)
            return "\n".join([doc['content'] for doc in res])
        return ""

    def _pre_chat(self, user_input: str) -> ChatPreTaskResult:
        if self.internet_search:
            knowledge_result = self.query_internet_search(user_input)
            return ChatPreTaskResult(result=True, args=knowledge_result)
        return ChatPreTaskResult(result=True)

    def _generate_conversation_input(self, input: str, args: Any) -> {}:
        try:
            if args:
                return  {"input": f"<问题> {input} </问题>\n<知识库内容> {args} </知识库内容>"}
            return  {"input": f"<问题> {input} </问题>"}
        except Exception as e:
            print(f"Error in chat_for_answer: {str(e)}")
            return None
