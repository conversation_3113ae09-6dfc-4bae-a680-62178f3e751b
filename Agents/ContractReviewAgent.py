import json
from typing import List

from langchain.chains import <PERSON><PERSON>hain
from pydantic import BaseModel

from Agents.BaseAnalysisAgent import BaseAnalysisAgent, AnalysisSqlRequest
from Models.agent.ContractRiskModel import ContractRiskList, ContractRiskResult, ContractRisk
from Services.ResourceService import INTER_LAW_KNOWLEDGE_ID


# 合同审查agent
class ContractReviewAgent(BaseAnalysisAgent):

    def get_kb_ids(self) -> list[int]:
        return [INTER_LAW_KNOWLEDGE_ID]

    def get_data_model(self) -> type[BaseModel]:
        return ContractRiskList

    def get_prompt(self) -> str:
        return """作为资深法律顾问，请分析以下合同条款：

                    请按以下要求输出内容：
                   1. 结合法律知识库的知识，和自身的专业知识，对条款进行风险评估。
                   2. 尽可能多的识别潜在法律风险。采用"主体-行为-责任"三维度挖掘隐性风险点。
                   3. 说明具体违反的法律条款，精确标注违法依据,并简要概述该条款（如《民法典》第一百九十三条，人民法院不得主动适用诉讼时效的规定。）
                   4. 提供"删除/重构/补充"三级整改路径
                   5. 评估风险等级分(高/中/低)
                   6. 禁止出现前后矛盾的内容，禁止出现重复的内容
                    """

    def filter_data(self, data: ContractRiskList) -> ContractRiskList | None:
        data.datas = [
            item for item in data.datas
            if item.legal_basis
               and item.legal_basis[-1] != "条"
               and item.clause
               and item.clause[-1] != "条"
        ]
        if data.datas:
            return data
        return None

    def save_extra(self, data: List[BaseModel]) -> str:
        result = {}
        data_res=self.deal_result(data)
        if data_res:
            result["high_risk_count"] = data_res.high_risk_count
            result["medium_risk_count"] = data_res.medium_risk_count
            result["low_risk_count"] = data_res.low_risk_count
            result["risk_total_count"] = data_res.risk_total_count
        return json.dumps(result)

    @staticmethod
    def deal_result(results: List[BaseModel]) -> BaseModel:
        """生成结构化风险评估报告"""
        contract_result = ContractRiskResult()
        for result in results:
            for item in result.datas:
                if isinstance(item, ContractRisk):
                    try:
                        if item.risk_level == "高":
                            contract_result.high_risk.append(item)
                        elif item.risk_level == "中":
                            contract_result.medium_risk.append(item)
                        else:
                            contract_result.low_risk.append(item)
                    except Exception:
                        print(f"风险评估失败{item}")
                        continue
        contract_result.high_risk_count = len(contract_result.high_risk)
        contract_result.medium_risk_count = len(contract_result.medium_risk)
        contract_result.low_risk_count = len(contract_result.low_risk)
        contract_result.risk_total_count = contract_result.high_risk_count + contract_result.medium_risk_count + contract_result.low_risk_count
        return contract_result
